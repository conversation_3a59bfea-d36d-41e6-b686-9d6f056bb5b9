#!/usr/bin/env python3
"""
Test Script for Reminder Manager

This script validates that the reminder manager works correctly with the new
models.py architecture. It tests all CRUD operations, error handling,
and data validation.

Usage:
    python test_reminder_manager.py

Expected Output:
    All tests should pass with detailed output showing the operations
    and their results.
"""

import sys
import os
from datetime import datetime, date, time
from typing import Dict, Any, List

# Add the tools directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tools', 'reminder'))

try:
    from tools.reminder.reminder_manager import (
        ReminderManager,
        create_reminder,
        list_reminders,
        get_reminder,
        update_reminder,
        delete_reminder,
        ReminderError,
        ReminderNotFoundError,
        ReminderValidationError,
        ReminderFileError
    )
    print("✓ Successfully imported reminder manager components")
except ImportError as e:
    print(f"✗ Failed to import reminder manager: {e}")
    sys.exit(1)


def test_reminder_creation():
    """Test creating reminders with various inputs"""
    print("\n=== Testing Reminder Creation ===")
    
    # Test basic reminder creation
    try:
        reminder1 = create_reminder(
            reminder_name="Doctor Appointment",
            reminder_description="Annual checkup with <PERSON><PERSON> Smith",
            reminder_date="2025-12-31",
            reminder_time="14:30",
            reminder_type="once"
        )
        print(f"✓ Created reminder: {reminder1['reminder_name']}")
        assert 'reminder_id' in reminder1
        assert reminder1['reminder_type'] == "once"
        assert reminder1['reminder_date'] == "2024-12-31"
        assert reminder1['reminder_time'] == "14:30"
        print(f"  - ID: {reminder1['reminder_id']}")
        print(f"  - Date: {reminder1['reminder_date']} at {reminder1['reminder_time']}")
        print(f"  - Type: {reminder1['reminder_type']}")
    except Exception as e:
        print(f"✗ Failed to create basic reminder: {e}")
        return False
    
    # Test daily reminder creation
    try:
        reminder2 = create_reminder(
            reminder_name="Daily Standup",
            reminder_description="Team standup meeting",
            reminder_date="2025-12-25",
            reminder_time="09:00",
            reminder_type="daily"
        )
        print(f"✓ Created daily reminder: {reminder2['reminder_name']}")
        assert reminder2['reminder_type'] == "daily"
    except Exception as e:
        print(f"✗ Failed to create daily reminder: {e}")
        return False
    
    # Test reminder creation with empty name (should fail)
    try:
        create_reminder(
            reminder_name="",
            reminder_description="This should fail",
            reminder_date="2024-12-31",
            reminder_time="12:00"
        )
        print("✗ Should have failed with empty name")
        return False
    except Exception as e:
        print(f"✓ Correctly rejected empty reminder name: {type(e).__name__}")
    
    # Test reminder creation with past date (should fail)
    try:
        create_reminder(
            reminder_name="Past Reminder",
            reminder_description="This should fail",
            reminder_date="2020-01-01",
            reminder_time="12:00"
        )
        print("✗ Should have failed with past date")
        return False
    except Exception as e:
        print(f"✓ Correctly rejected past date: {type(e).__name__}")
    
    return True


def test_reminder_listing():
    """Test listing all reminders"""
    print("\n=== Testing Reminder Listing ===")
    
    try:
        reminders = list_reminders()
        print(f"✓ Listed {len(reminders)} reminders")
        
        # Show sample reminders
        for i, reminder in enumerate(reminders[:3]):
            print(f"  {i+1}. {reminder['reminder_name']} ({reminder['reminder_type']})")
            print(f"     {reminder['reminder_date']} at {reminder['reminder_time']}")
        
        if len(reminders) > 3:
            print(f"  ... and {len(reminders) - 3} more reminders")
            
        return True
    except Exception as e:
        print(f"✗ Failed to list reminders: {e}")
        return False


def test_reminder_retrieval():
    """Test getting specific reminders by ID"""
    print("\n=== Testing Reminder Retrieval ===")
    
    try:
        reminders = list_reminders()
        if not reminders:
            print("! No reminders available for retrieval test")
            return True
            
        first_reminder_id = reminders[0]['reminder_id']
        retrieved_reminder = get_reminder(first_reminder_id)
        
        print(f"✓ Retrieved reminder: {retrieved_reminder['reminder_name']}")
        assert retrieved_reminder['reminder_id'] == first_reminder_id
        assert 'reminder_description' in retrieved_reminder
        assert 'reminder_date' in retrieved_reminder
        assert 'reminder_time' in retrieved_reminder
        
        # Test retrieving non-existent reminder
        try:
            get_reminder("nonexistent_id_12345")
            print("✗ Should have failed with non-existent ID")
            return False
        except ReminderNotFoundError:
            print("✓ Correctly handled non-existent reminder ID")
        
        return True
    except Exception as e:
        print(f"✗ Failed to retrieve reminder: {e}")
        return False


def test_reminder_update():
    """Test updating reminders"""
    print("\n=== Testing Reminder Updates ===")
    
    # Create a reminder to update
    try:
        original_reminder = create_reminder(
            reminder_name="Update Test Reminder",
            reminder_description="This reminder will be updated",
            reminder_date="2025-12-31",
            reminder_time="12:00",
            reminder_type="once"
        )
        print(f"✓ Created reminder for update test: {original_reminder['reminder_id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create reminder for update: {e}")
        return False
    
    # Test updating name only
    try:
        updated_reminder = update_reminder(
            original_reminder['reminder_id'], 
            reminder_name="Updated Reminder Name"
        )
        print("✓ Updated reminder name")
        assert updated_reminder['reminder_name'] == "Updated Reminder Name"
        assert updated_reminder['reminder_description'] == "This reminder will be updated"  # Should remain unchanged
    except Exception as e:
        print(f"✗ Failed to update reminder name: {e}")
        return False
    
    # Test updating time and type
    try:
        updated_reminder = update_reminder(
            original_reminder['reminder_id'],
            reminder_time="15:30",
            reminder_type="daily"
        )
        print("✓ Updated reminder time and type")
        assert updated_reminder['reminder_time'] == "15:30"
        assert updated_reminder['reminder_type'] == "daily"
    except Exception as e:
        print(f"✗ Failed to update reminder time and type: {e}")
        return False
    
    # Test updating all fields
    try:
        updated_reminder = update_reminder(
            original_reminder['reminder_id'],
            reminder_name="Final Name",
            reminder_description="Final description",
            reminder_date="2025-01-01",
            reminder_time="10:00",
            reminder_type="weekly"
        )
        print("✓ Updated all reminder fields")
        assert updated_reminder['reminder_name'] == "Final Name"
        assert updated_reminder['reminder_description'] == "Final description"
        assert updated_reminder['reminder_date'] == "2025-01-01"
        assert updated_reminder['reminder_time'] == "10:00"
        assert updated_reminder['reminder_type'] == "weekly"
    except Exception as e:
        print(f"✗ Failed to update all fields: {e}")
        return False
    
    return True


def test_reminder_deletion():
    """Test deleting reminders"""
    print("\n=== Testing Reminder Deletion ===")
    
    # Create a reminder to delete
    try:
        reminder_to_delete = create_reminder(
            reminder_name="Delete Test Reminder",
            reminder_description="This reminder will be deleted",
            reminder_date="2025-12-31",
            reminder_time="12:00"
        )
        print(f"✓ Created reminder for deletion test: {reminder_to_delete['reminder_id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create reminder for deletion: {e}")
        return False
    
    # Test successful deletion
    try:
        result = delete_reminder(reminder_to_delete['reminder_id'])
        print(f"✓ Deleted reminder: {result}")
        assert result == True
    except Exception as e:
        print(f"✗ Failed to delete reminder: {e}")
        return False
    
    # Verify reminder is gone
    try:
        get_reminder(reminder_to_delete['reminder_id'])
        print("✗ Reminder should have been deleted but still exists")
        return False
    except ReminderNotFoundError:
        print("✓ Confirmed reminder was deleted")
    
    # Test deleting non-existent reminder
    try:
        result = delete_reminder("nonexistent_id_12345")
        print(f"✓ Deletion of non-existent reminder returned: {result}")
        assert result == False
    except Exception as e:
        print(f"✗ Unexpected error deleting non-existent reminder: {e}")
        return False
    
    return True


def test_reminder_types():
    """Test different reminder types"""
    print("\n=== Testing Reminder Types ===")
    
    reminder_types = ["once", "daily", "weekly", "monthly"]
    
    for reminder_type in reminder_types:
        try:
            reminder = create_reminder(
                reminder_name=f"Test {reminder_type.title()} Reminder",
                reminder_description=f"Testing {reminder_type} reminder type",
                reminder_date="2025-12-31",
                reminder_time="12:00",
                reminder_type=reminder_type
            )
            print(f"✓ Created {reminder_type} reminder")
            assert reminder['reminder_type'] == reminder_type
        except Exception as e:
            print(f"✗ Failed to create {reminder_type} reminder: {e}")
            return False
    
    # Test invalid reminder type (should fail)
    try:
        create_reminder(
            reminder_name="Invalid Type Reminder",
            reminder_description="This should fail",
            reminder_date="2024-12-31",
            reminder_time="12:00",
            reminder_type="invalid_type"
        )
        print("✗ Should have failed with invalid reminder type")
        return False
    except Exception as e:
        print(f"✓ Correctly rejected invalid reminder type: {type(e).__name__}")
    
    return True


def run_all_tests():
    """Run all reminder manager tests"""
    print("Starting Reminder Manager Tests")
    print("=" * 50)
    
    tests = [
        test_reminder_creation,
        test_reminder_listing,
        test_reminder_retrieval,
        test_reminder_update,
        test_reminder_deletion,
        test_reminder_types
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Reminder Manager Tests Complete: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Reminder manager is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
