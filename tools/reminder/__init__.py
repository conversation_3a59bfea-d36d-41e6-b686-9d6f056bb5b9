import json
from .reminder_manager import <PERSON>minder<PERSON>anager

reminder_manager = ReminderManager()

__all__ = [
    "create_reminder",
    "list_reminders",
    "get_reminder",
    "delete_reminder"
]

def create_reminder(*args, **kwargs):
    """Wrapper for create_reminder with JSON validation"""
    try:
        result = reminder_manager.create_reminder(*args, **kwargs)
        # Ensure result is JSON serializable
        json.dumps(result)
        return result
    except Exception as e:
        return {"error": str(e), "success": False}

def list_reminders(*args, **kwargs):
    """Wrapper for list_reminders with JSON validation"""
    try:
        result = reminder_manager.list_reminders(*args, **kwargs)
        # Ensure result is JSON serializable
        json.dumps(result)
        return result
    except Exception as e:
        return {"error": str(e), "success": False}

def get_reminder(*args, **kwargs):
    """Wrapper for get_reminder with JSON validation"""
    try:
        result = reminder_manager.get_reminder(*args, **kwargs)
        # Ensure result is JSON serializable
        json.dumps(result)
        return result
    except Exception as e:
        return {"error": str(e), "success": False}

def delete_reminder(*args, **kwargs):
    """Wrapper for delete_reminder with JSON validation"""
    try:
        result = reminder_manager.delete_reminder(*args, **kwargs)
        # Ensure result is JSON serializable
        json.dumps(result)
        return result
    except Exception as e:
        return {"error": str(e), "success": False}