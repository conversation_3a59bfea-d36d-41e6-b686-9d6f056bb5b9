"""
Reminder Manager Module

This module provides a comprehensive reminder management system with consistent architecture
using centralized models from models.py. It handles CRUD operations for reminders with
proper validation, error handling, and caching mechanisms.

Features:
- Create, read, update, delete reminders
- Reminder types (once, daily, weekly, monthly)
- Date and time validation
- Caching for performance
- Consistent data validation using Pydantic models
- Comprehensive error handling
"""

import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import ValidationError
import time
from models import ReminderBase, ReminderCreate, ReminderUpdate, ReminderResponse


# Custom exception classes for different reminder errors
class ReminderError(Exception):
    """Base exception for reminder operations"""
    pass


class ReminderNotFoundError(ReminderError):
    """Exception raised when a reminder is not found"""
    pass


class ReminderValidationError(ReminderError):
    """Exception raised for reminder validation errors"""
    pass


class ReminderFileError(ReminderError):
    """Exception raised for file operation errors"""
    pass
    
class ReminderManager:
    """
    Comprehensive reminder management system with consistent architecture.

    This class handles all CRUD operations for reminders using centralized models
    from models.py. It provides caching, validation, and error handling.

    Attributes:
        storage_path (str): Path to the reminders storage file
        reminders_cache (List[Dict]): Cached reminders for performance
        cache_timestamp (float): Timestamp of last cache update
        cache_ttl (int): Cache time-to-live in seconds
    """

    def __init__(self, storage_path: str = "reminders.jsonl"):
        """
        Initialize the ReminderManager with storage configuration.

        Args:
            storage_path (str): Path to the JSONL file for storing reminders
        """
        try:
            self.storage_path = storage_path
            self.ensure_storage_path()
            self.reminders_cache = None
            self.cache_timestamp = 0
            self.cache_ttl = 60  # Cache valid for 60 seconds
        except Exception as e:
            raise ReminderError(f"Error initializing ReminderManager: {e}")

    def ensure_storage_path(self):
        """
        Ensure the storage file exists, create if missing.

        Raises:
            ReminderFileError: If file creation fails
        """
        try:
            if not os.path.exists(self.storage_path):
                with open(self.storage_path, "w") as f:
                    pass  # Create empty file
        except Exception as e:
            raise ReminderFileError(f"Error creating storage file: {e}")

    def _is_cache_valid(self) -> bool:
        """
        Check if the cache is still valid based on file modification time and TTL.

        Returns:
            bool: True if cache is valid, False otherwise
        """
        if self.reminders_cache is None:
            return False
        if not os.path.exists(self.storage_path):
            return self.cache_timestamp > time.time() - self.cache_ttl
        file_mtime = os.path.getmtime(self.storage_path)
        # Cache is valid if file has not changed and TTL not expired
        return self.cache_timestamp > file_mtime and (time.time() - self.cache_timestamp < self.cache_ttl)

    def _load_reminders(self) -> List[Dict[str, Any]]:
        """
        Load reminders from cache or file with proper error handling.

        Returns:
            List[Dict[str, Any]]: List of reminder dictionaries

        Raises:
            ReminderFileError: If file operations fail
        """
        if self._is_cache_valid():
            return self.reminders_cache

        try:
            with open(self.storage_path, "r") as f:
                self.reminders_cache = [json.loads(line.strip()) for line in f if line.strip()]
                self.cache_timestamp = time.time()
                return self.reminders_cache
        except FileNotFoundError:
            self.reminders_cache = []
            self.cache_timestamp = time.time()
            return self.reminders_cache
        except json.JSONDecodeError as e:
            raise ReminderFileError(f"Error decoding reminders file: {e}")
        except Exception as e:
            raise ReminderFileError(f"Error loading reminders: {e}")

    def _save_reminders(self, reminders: List[Dict[str, Any]]) -> None:
        """
        Save reminders to file and update cache.

        Args:
            reminders (List[Dict[str, Any]]): List of reminder dictionaries to save

        Raises:
            ReminderFileError: If file operations fail
        """
        try:
            with open(self.storage_path, "w") as f:
                for reminder in reminders:
                    f.write(json.dumps(reminder) + '\n')
            self.reminders_cache = reminders
            self.cache_timestamp = time.time()
        except Exception as e:
            raise ReminderFileError(f"Error saving reminders: {e}")

    def create_reminder(self, reminder_name: str, reminder_description: str,
                       reminder_date: str, reminder_time: str,
                       reminder_type: str = "once") -> Dict[str, Any]:
        """
        Create a new reminder with validation and proper error handling.

        Args:
            reminder_name (str): Name of the reminder
            reminder_description (str): Description of the reminder
            reminder_date (str): Date for the reminder
            reminder_time (str): Time for the reminder
            reminder_type (str): Type of reminder (once, daily, weekly, monthly)

        Returns:
            Dict[str, Any]: The created reminder data

        Raises:
            ReminderValidationError: If reminder data is invalid
            ReminderFileError: If file operations fail
        """
        try:
            # Create reminder using the centralized model
            reminder_data = ReminderCreate(
                reminder_name=reminder_name,
                reminder_description=reminder_description,
                reminder_date=reminder_date,
                reminder_time=reminder_time,
                reminder_type=reminder_type
            )

            # Load existing reminders and add the new one
            reminders = self._load_reminders()
            reminder_dict = reminder_data.model_dump()
            reminders.append(reminder_dict)

            # Save all reminders back to file
            self._save_reminders(reminders)

            return reminder_dict

        except ValidationError as e:
            raise ReminderValidationError(f"Invalid reminder data: {e}")
        except Exception as e:
            raise ReminderError(f"Error creating reminder: {e}")
        
    def list_reminders(self) -> List[Dict[str, Any]]:
        """
        List all reminders with proper error handling.

        Returns:
            List[Dict[str, Any]]: List of all reminders

        Raises:
            ReminderFileError: If file operations fail
        """
        try:
            reminders = self._load_reminders()
            return reminders
        except Exception as e:
            raise ReminderError(f"Error listing reminders: {e}")

    def get_reminder(self, reminder_id: str) -> Dict[str, Any]:
        """
        Get a specific reminder by its ID.

        Args:
            reminder_id (str): The ID of the reminder to retrieve

        Returns:
            Dict[str, Any]: The reminder data

        Raises:
            ReminderNotFoundError: If reminder with given ID is not found
            ReminderError: If retrieval operation fails
        """
        try:
            reminders = self._load_reminders()
            for reminder in reminders:
                if reminder.get('reminder_id') == reminder_id:
                    return reminder
            raise ReminderNotFoundError(f"Reminder with ID {reminder_id} not found")
        except ReminderNotFoundError:
            raise
        except Exception as e:
            raise ReminderError(f"Error getting reminder: {e}")

    def delete_reminder(self, reminder_id: str) -> bool:
        """
        Delete a reminder by its ID.

        Args:
            reminder_id (str): The ID of the reminder to delete

        Returns:
            bool: True if reminder was deleted, False if not found

        Raises:
            ReminderError: If delete operation fails
        """
        try:
            reminders = self._load_reminders()

            # Find and remove the reminder
            for i, reminder in enumerate(reminders):
                if reminder.get('reminder_id') == reminder_id:
                    del reminders[i]
                    self._save_reminders(reminders)
                    return True

            return False

        except Exception as e:
            raise ReminderError(f"Error deleting reminder: {e}")

    def update_reminder(self, reminder_id: str, reminder_name: str = None,
                       reminder_description: str = None, reminder_date: str = None,
                       reminder_time: str = None, reminder_type: str = None) -> Dict[str, Any]:
        """
        Update an existing reminder with new data.

        Args:
            reminder_id (str): The ID of the reminder to update
            reminder_name (str, optional): New name for the reminder
            reminder_description (str, optional): New description for the reminder
            reminder_date (str, optional): New date for the reminder
            reminder_time (str, optional): New time for the reminder
            reminder_type (str, optional): New type for the reminder

        Returns:
            Dict[str, Any]: The updated reminder data

        Raises:
            ReminderNotFoundError: If reminder with given ID is not found
            ReminderValidationError: If update data is invalid
            ReminderError: If update operation fails
        """
        try:
            reminders = self._load_reminders()

            # Find the reminder to update
            reminder_index = None
            for i, reminder in enumerate(reminders):
                if reminder.get('reminder_id') == reminder_id:
                    reminder_index = i
                    break

            if reminder_index is None:
                raise ReminderNotFoundError(f"Reminder with ID {reminder_id} not found")

            # Prepare update data
            update_data = {}
            if reminder_name is not None:
                update_data['reminder_name'] = reminder_name
            if reminder_description is not None:
                update_data['reminder_description'] = reminder_description
            if reminder_date is not None:
                update_data['reminder_date'] = reminder_date
            if reminder_time is not None:
                update_data['reminder_time'] = reminder_time
            if reminder_type is not None:
                update_data['reminder_type'] = reminder_type

            # Validate update data using the model
            if update_data:
                try:
                    ReminderUpdate(**update_data)
                except ValidationError as e:
                    raise ReminderValidationError(f"Invalid update data: {e}")

                # Apply updates
                reminders[reminder_index].update(update_data)

                # Save updated reminders
                self._save_reminders(reminders)

            return reminders[reminder_index]

        except (ReminderNotFoundError, ReminderValidationError):
            raise
        except Exception as e:
            raise ReminderError(f"Error updating reminder: {e}")


# Create a default instance for easy import and use
reminder_manager = ReminderManager()

# Convenience functions for easy import
def create_reminder(reminder_name: str, reminder_description: str, reminder_date: str,
                   reminder_time: str, reminder_type: str = "once") -> Dict[str, Any]:
    """Create a new reminder using the default manager"""
    return reminder_manager.create_reminder(reminder_name, reminder_description,
                                          reminder_date, reminder_time, reminder_type)

def list_reminders() -> List[Dict[str, Any]]:
    """List all reminders using the default manager"""
    return reminder_manager.list_reminders()

def get_reminder(reminder_id: str) -> Dict[str, Any]:
    """Get a reminder by ID using the default manager"""
    return reminder_manager.get_reminder(reminder_id)

def update_reminder(reminder_id: str, reminder_name: str = None,
                   reminder_description: str = None, reminder_date: str = None,
                   reminder_time: str = None, reminder_type: str = None) -> Dict[str, Any]:
    """Update a reminder using the default manager"""
    return reminder_manager.update_reminder(reminder_id, reminder_name, reminder_description,
                                          reminder_date, reminder_time, reminder_type)

def delete_reminder(reminder_id: str) -> bool:
    """Delete a reminder using the default manager"""
    return reminder_manager.delete_reminder(reminder_id)




