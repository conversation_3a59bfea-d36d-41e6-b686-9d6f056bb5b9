import os
import json
from datetime import datetime
from typing import List, Dict, Any, Literal
from uuid import uuid4
from pydantic import BaseModel, Field, ValidationError
import time

# Custom exception classes for different reminder errors
class ReminderError(Exception):
    """Bazowy wyjątek dla przypomnień"""
    pass

class ReminderNotFoundError(ReminderError):
    """Wyjątek dla nieznalezionego przypomnienia"""
    pass

class ReminderValidationError(ReminderError):
    """Wyjątek dla błędów walidacji przypomnienia"""
    pass

class ReminderFileError(ReminderError):
    """Wyjątek dla błędów związanych z plikiem przypomnień"""
    pass

# Reminder model using Pydantic for validation and serialization
class Reminder(BaseModel):
    reminder_id: str = Field(default_factory=lambda: str(uuid4()))  # Unique ID for reminder
    reminder_name: str = Field(..., min_length=1, max_length=100)  # Name of reminder
    reminder_description: str = Field(..., min_length=1, max_length=2000)  # Description
    reminder_date: str = Field(..., min_length=1, max_length=100)  # Date of reminder
    reminder_time: str = Field(..., min_length=1, max_length=100)  # Time of reminder
    reminder_created_date: str = Field(default_factory=lambda: datetime.now().isoformat())  # Creation date
    reminder_type: Literal["once", "daily"] = Field(...)  # Type: once or daily

    class Config: 
        # Example schema for documentation
        reminder_schema = {
            "example": {
                "reminder_name": "Buy milk",
                "reminder_description": "Buy 2L of milk",
                "reminder_date": "2021-01-01",
                "reminder_time": "12:00",
                "reminder_type": "once"
            }
        }
    
# Manager class for reminders: handles CRUD and file operations
class ReminderManager:
    def __init__(self, storage_path: str = "reminders.jsonl"):
        """Initializes ReminderManager and ensures storage file exists"""
        try:
            self.storage_path = storage_path  # Path to reminders file
            self.ensure_storage_path()  # Create file if not exists
            self.reminder_schema = Reminder.Config.reminder_schema  # Reminder schema
            self.reminders_cache = None  # Cache for reminders
            self.cache_timestamp = 0  # Timestamp for cache
            self.cache_ttl = 60  # Cache time-to-live in seconds
        except ReminderError as e:
            raise e
    
    def ensure_storage_path(self):
        """Ensures the storage file exists, creates if missing"""
        if not os.path.exists(self.storage_path):
            with open(self.storage_path, "w") as f:
                pass  # Create empty file

    def _is_cache_valid(self) -> bool:
        """Checks if the cache is still valid"""
        if self.reminders_cache is None:
            return False
        if not os.path.exists(self.storage_path):
            return False
        file_mtime = os.path.getmtime(self.storage_path)
        # Cache is valid if file has not changed and TTL not expired
        return self.cache_timestamp > file_mtime and (time.time() - self.cache_timestamp < self.cache_ttl)
    
    def _load_reminders(self) -> List[Dict[str, Any]]:
        """Loads reminders from cache or file"""
        if self._is_cache_valid():
            return self.reminders_cache
        try:
            with open(self.storage_path, "r") as f:
                self.reminders_cache = [json.loads(line) for line in f]
                self.cache_timestamp = time.time()
                return self.reminders_cache
        except (FileNotFoundError, json.JSONDecodeError):
            self.reminders_cache = []
            self.cache_timestamp = time.time()
            return self.reminders_cache
        
    def _save_reminders(self, reminders: List[Dict[str, Any]]) -> None:
        """Saves reminders to the file"""
        try:
            with open(self.storage_path, "w") as f:
                for reminder in reminders:
                    f.write(json.dumps(reminder) + '\n')
            self.reminders_cache = reminders
            self.cache_timestamp = time.time()
        except OSError as e:
            print(f"Error saving reminders: {e}")

    def create_reminder(self, reminder_name: str, reminder_description: str, 
                       reminder_date: str, reminder_time: str, 
                       reminder_type: str = "once") -> Dict[str, Any]:
        """Creates a new reminder and saves it"""
        try:
            reminder = Reminder(
                reminder_name=reminder_name,
                reminder_description=reminder_description,
                reminder_date=reminder_date,
                reminder_time=reminder_time,
                reminder_type=reminder_type
            )
            reminders = self._load_reminders()
            reminders.append(reminder.model_dump())
            self._save_reminders(reminders)
            return {"reminder": reminder.model_dump(), "name": reminder.reminder_name, "status": "reminder created"}
        except ValidationError as e:
            raise ReminderValidationError(f"Invalid reminder data: {e}")
        except FileNotFoundError as e:
            raise ReminderFileError(f"Reminders file not found: {e}")
        except Exception as e:
            raise ReminderError(f"Error creating reminder: {e}")
        
    def list_reminders(self) -> List[Dict[str, Any]]:
        """Lists all reminders"""
        try:
            reminders = self._load_reminders()
            return reminders
        except FileNotFoundError as e:
            raise ReminderFileError(f"Reminders file not found: {e}")
        except json.JSONDecodeError as e:
            raise ReminderFileError(f"Error decoding reminders file: {e}")
        except Exception as e:
            raise ReminderError(f"Error listing reminders: {e}")
        
    def get_reminder(self, reminder_id: str) -> Dict[str, Any]:
        """Gets a reminder by its ID"""
        try:
            reminders = self._load_reminders()
            for reminder in reminders:
                if reminder['reminder_id'] == reminder_id:
                    return reminder
            raise ReminderNotFoundError(f"Reminder with ID {reminder_id} not found")
        except FileNotFoundError as e:
            raise ReminderFileError(f"Reminders file not found: {e}")
        except json.JSONDecodeError as e:
            raise ReminderFileError(f"Error decoding reminders file: {e}")
        except Exception as e:
            raise ReminderError(f"Error getting reminder: {e}") 
        
    def delete_reminder(self, reminder_id: str) -> bool:
        """Deletes a reminder by its ID"""
        try:
            reminders = self._load_reminders()
            for i, reminder in enumerate(reminders):
                if reminder['reminder_id'] == reminder_id:
                    del reminders[i]
                    self._save_reminders(reminders)
                    return True
            raise ReminderNotFoundError(f"Reminder with ID {reminder_id} not found")
        except FileNotFoundError as e:
            raise ReminderFileError(f"Reminders file not found: {e}")
        except json.JSONDecodeError as e:
            raise ReminderFileError(f"Error decoding reminders file: {e}")
        except Exception as e:
            raise ReminderError(f"Error deleting reminder: {e}")




