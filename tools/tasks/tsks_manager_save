"""Task tools
    1. Create a task     #done                     
    2. List tasks #done
    3. Delete task #done
    4. Update task #done
    5. Get task #done
        -tasks in jsonline format
            task_id: int
            task_name: str
            task_description: str
            task_status: str
            task_priority: str
            task_due_date: str(datetime)
            task_created_date: str(datetime)
            task_modified_date: str(datetime)
            task_completed_date: str(datetime)
            task_tags: list[str]
            task_comments: list[str]    
 Implementation:
 1. loading tasks file function for reading and writing/editing tasks #done
 2. create task function #done
 3. list tasks function #done
 4. delete task function #done 
 5. update task function #done
 6. get task function #done 
 7. task tools class #current
 8. caching tasks in memory #while writing classes
 9. error handling #half done
 10. register task tools in agent #pending           
    """
"""1 Search/filter - This would make the system much more practical                                                                                                                                                                                                
 2 Single task details view - Essential for viewing specific task info                                                                                                                                                                                           
 3 Time-based views - Crucial for daily task management                                                                                                                                                                                                          
 4 Partial updates - More convenient than updating all fields """

"""#Task tool loader
@Kirk.tool_plain
def load_tasks():
    try:
        with open("tasks.jsonl", 'r') as f:
            tasks = [json.loads(line) for line in f]
    except FileNotFoundError:
        tasks = []
    return tasks

#tasks creator
@Kirk.tool_plain
def create_task(task_name: str, task_description: str, task_status: str, task_priority: str, task_due_date: str, task_tags: list[str], task_comments: list[str]) -> Dict[str, Any]:
    task_id = uuid4().hex
    task = {
        'task_id': task_id,
        'task_name': task_name,
        'task_description': task_description,
        'task_status': task_status,
        'task_priority': task_priority,
        "task_due_date": task_due_date,
        'task_created_date': str(datetime.now()),
        'task_modified_date': str(datetime.now()),
        'task_completed_date': None if task_status != 'completed' else str(datetime.now()),
        'task_tags': task_tags,
        'task_comments': task_comments
    }
    with open('tasks.jsonl', 'a') as f:#append
        f.write(json.dumps(task) + '\n')#write task to file
    return (task_id, task_name) #return task id and name

#list tasks only taks name
@Kirk.tool_plain
def list_tasks():
    try:
        with open('tasks.jsonl', 'r') as f:
            tasks = [json.loads(line) for line in f] #read tasks from file
    except FileNotFoundError:
        tasks = []
    return [task['task_name'] for task in tasks] #return list of task names

#delete task
@Kirk.tool_plain
def delete_task(task_id: str):
    try:
        # Read existing tasks
        with open('tasks.jsonl', 'r') as f:
            tasks = [json.loads(line) for line in f]#read tasks from file
        
        # Filter out the task to be deleted
        original_count = len(tasks)#count tasks before deletion #
        tasks = [task for task in tasks if task['task_id'] != task_id]#filter out task to be deleted
        
        # Check if a task was actually deleted
        if len(tasks) == original_count:
            return False  # No task was deleted
        
        # Write the updated list back to the file
        with open('tasks.jsonl', 'w') as f:
            for task in tasks:
                f.write(json.dumps(task) + '\n')
        
        return True  # Successfully deleted
    
    except FileNotFoundError:
        # If the file doesn't exist, create an empty one
        with open('tasks.jsonl', 'w') as f:
            pass
        return False  # No task was deleted

#update task
@Kirk.tool_plain
def update_task(task_id: str, task_name: str, task_description: str, task_status: str, task_priority: str, task_due_date: str, task_tags: list[str], task_comments: list[str]):
    try:
        with open('tasks.jsonl', 'r') as f:
            tasks = [json.loads(line) for line in f]
    except FileNotFoundError:
        tasks = []
        return False  # No task was updated
    task = next((task for task in tasks if task['task_id'] == task_id), None)
    if task is None:
        return False  # No task was updated
    task['task_name'] = task_name
    task['task_description'] = task_description
    task['task_status'] = task_status
    task['task_priority'] = task_priority
    task['task_due_date'] = task_due_date
    task['task_modified_date'] = str(datetime.now())
    task['task_tags'] = task_tags
    task['task_comments'] = task_comments
    with open('tasks.jsonl', 'w') as f:
        for task in tasks:
            f.write(json.dumps(task) + '\n')
    return True  # Successfully updated

#get task
@Kirk.tool_plain
def get_task(task_id: str):
    try:
        with open('tasks.jsonl', 'r') as f:
            tasks = [json.loads(line) for line in f]
    except FileNotFoundError:
        tasks = []
        return None  # No task was found
    task = next((task for task in tasks if task['task_id'] == task_id), None)
    return task  # Return the task

    if task is None:
        return None  # No task was found"""