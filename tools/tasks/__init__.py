# init file for tasks tools
from .task_manager import TaskManager

task_manager = TaskManager()

# Eksportujemy metody jako gotowe narzędzia
__all__ = [
    'create_task',
    'list_tasks', 
    'delete_task',
    'update_task',
    'get_task'
]

# Eksportujemy metody dla łatwego importu
create_task = task_manager.create_task
list_tasks = task_manager.list_tasks
delete_task = task_manager.delete_task
update_task = task_manager.update_task
get_task = task_manager.get_task