"""
Task Manager Module

This module provides a comprehensive task management system with consistent architecture
using centralized models from models.py. It handles CRUD operations for tasks with
proper validation, error handling, and caching mechanisms.

Features:
- Create, read, update, delete tasks
- Task status management (todo, in_progress, completed, cancelled)
- Priority levels (low, medium, high, urgent)
- Tag and comment management
- Caching for performance
- Consistent data validation using Pydantic models
- Comprehensive error handling
"""

import json
import os
from time import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import ValidationError
from models import TaskBase, TaskCreate, TaskUpdate, TaskResponse


# Custom exception classes for different task errors
class TaskError(Exception):
    """Base exception for task operations"""
    pass


class TaskNotFoundError(TaskError):
    """Exception raised when a task is not found"""
    pass


class TaskValidationError(TaskError):
    """Exception raised for task validation errors"""
    pass


class TaskFileError(TaskError):
    """Exception raised for file operation errors"""
    pass

class TaskManager:
    """
    Comprehensive task management system with consistent architecture.

    This class handles all CRUD operations for tasks using centralized models
    from models.py. It provides caching, validation, and error handling.

    Attributes:
        storage_path (str): Path to the tasks storage file
        tasks_cache (List[Dict]): Cached tasks for performance
        cache_timestamp (float): Timestamp of last cache update
        cache_ttl (int): Cache time-to-live in seconds
    """

    def __init__(self, storage_path: str = "tasks.jsonl"):
        """
        Initialize the TaskManager with storage configuration.

        Args:
            storage_path (str): Path to the JSONL file for storing tasks
        """
        self.storage_path = storage_path
        self.tasks_cache = None
        self.cache_timestamp = 0
        self.cache_ttl = 60  # Cache valid for 60 seconds

    def _is_cache_valid(self) -> bool:
        """
        Check if the cache is still valid based on file modification time and TTL.

        Returns:
            bool: True if cache is valid, False otherwise
        """
        if self.tasks_cache is None:
            return False
        if not os.path.exists(self.storage_path):
            return self.cache_timestamp > time() - self.cache_ttl
        file_mtime = os.path.getmtime(self.storage_path)
        # Cache is valid if cache is recent and file has not changed since cache_timestamp
        return self.cache_timestamp > time() - self.cache_ttl and file_mtime < self.cache_timestamp

    def _load_tasks(self) -> List[Dict[str, Any]]:
        """
        Load tasks from cache or file with proper error handling.

        Returns:
            List[Dict[str, Any]]: List of task dictionaries

        Raises:
            TaskFileError: If file operations fail
        """
        if self._is_cache_valid():
            return self.tasks_cache

        try:
            with open(self.storage_path, "r") as f:
                content = f.read().strip()
                if not content:
                    # Empty file is valid, return empty list
                    self.tasks_cache = []
                    self.cache_timestamp = time()
                    return self.tasks_cache

                # Parse each line that has content
                self.tasks_cache = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line:
                        try:
                            self.tasks_cache.append(json.loads(line))
                        except json.JSONDecodeError as e:
                            # Skip malformed lines but continue processing
                            print(f"Warning: Skipping malformed line in tasks file: {line[:50]}...")
                            continue

                self.cache_timestamp = time()
                return self.tasks_cache
        except FileNotFoundError:
            self.tasks_cache = []
            self.cache_timestamp = time()
            return self.tasks_cache
        except Exception as e:
            raise TaskFileError(f"Error loading tasks: {e}")

    def _save_tasks(self, tasks: List[Dict[str, Any]]) -> None:
        """
        Save tasks to file and update cache.

        Args:
            tasks (List[Dict[str, Any]]): List of task dictionaries to save

        Raises:
            TaskFileError: If file operations fail
        """
        try:
            with open(self.storage_path, 'w') as f:
                for task in tasks:
                    f.write(json.dumps(task) + '\n')
            self.tasks_cache = tasks
            self.cache_timestamp = time()
        except Exception as e:
            raise TaskFileError(f"Error saving tasks: {e}")

    def create_task(self, task_name: str, task_description: str,
                   task_due_date: str, task_status: str = "todo",
                   task_priority: str = "medium", task_tags: List[str] = None,
                   task_comments: List[str] = None) -> Dict[str, Any]:
        """
        Create a new task with validation and proper error handling.

        Args:
            task_name (str): Name of the task
            task_description (str): Description of the task
            task_due_date (str): Due date for the task
            task_status (str): Status of the task (todo, in_progress, completed, cancelled)
            task_priority (str): Priority level (low, medium, high, urgent)
            task_tags (List[str], optional): List of tags for the task
            task_comments (List[str], optional): List of comments for the task

        Returns:
            Dict[str, Any]: The created task data

        Raises:
            TaskValidationError: If task data is invalid
            TaskFileError: If file operations fail
        """
        try:
            # Set default values for optional parameters
            if task_tags is None:
                task_tags = []
            if task_comments is None:
                task_comments = []

            # Validate required fields
            if not task_due_date:
                raise TaskValidationError("task_due_date is required and cannot be empty")

            # Create task using the centralized model
            task_data = TaskCreate(
                task_name=task_name,
                task_description=task_description,
                task_status=task_status,
                task_priority=task_priority,
                task_due_date=task_due_date,
                task_tags=task_tags,
                task_comments=task_comments
            )

            # Load existing tasks and add the new one
            tasks = self._load_tasks()
            task_dict = task_data.model_dump()
            tasks.append(task_dict)

            # Save all tasks back to file
            self._save_tasks(tasks)

            return task_dict

        except ValidationError as e:
            raise TaskValidationError(f"Invalid task data: {e}")
        except Exception as e:
            raise TaskError(f"Error creating task: {e}")

    def get_task(self, task_id: str) -> Dict[str, Any]:
        """
        Get a specific task by its ID.

        Args:
            task_id (str): The ID of the task to retrieve

        Returns:
            Dict[str, Any]: The task data

        Raises:
            TaskNotFoundError: If task with given ID is not found
            TaskError: If retrieval operation fails
        """
        try:
            tasks = self._load_tasks()
            for task in tasks:
                if task.get('task_id') == task_id:
                    return task
            raise TaskNotFoundError(f"Task with ID {task_id} not found")
        except TaskNotFoundError:
            raise
        except Exception as e:
            raise TaskError(f"Error retrieving task: {e}")

    def update_task(self, task_id: str, **kwargs) -> Dict[str, Any]:
        """
        Update an existing task with new data.

        Args:
            task_id (str): The ID of the task to update
            **kwargs: Fields to update (task_name, task_description, task_status, etc.)

        Returns:
            Dict[str, Any]: The updated task data

        Raises:
            TaskNotFoundError: If task with given ID is not found
            TaskValidationError: If update data is invalid
            TaskError: If update operation fails
        """
        try:
            tasks = self._load_tasks()

            # Find the task to update
            task_index = None
            for i, task in enumerate(tasks):
                if task.get('task_id') == task_id:
                    task_index = i
                    break

            if task_index is None:
                raise TaskNotFoundError(f"Task with ID {task_id} not found")

            # Validate update data using the model if any fields are provided
            if kwargs:
                try:
                    TaskUpdate(**kwargs)
                except ValidationError as e:
                    raise TaskValidationError(f"Invalid update data: {e}")

                # Apply updates
                for key, value in kwargs.items():
                    if hasattr(TaskBase, key):  # Only update valid fields
                        tasks[task_index][key] = value

                # Always update modification date
                tasks[task_index]['task_modified_date'] = datetime.now().isoformat()

                # If status is "completed", set completion date
                if (tasks[task_index].get('task_status') == 'completed' and
                    not tasks[task_index].get('task_completed_date')):
                    tasks[task_index]['task_completed_date'] = datetime.now().isoformat()

                # Save updated tasks
                self._save_tasks(tasks)

            return tasks[task_index]

        except (TaskNotFoundError, TaskValidationError):
            raise
        except Exception as e:
            raise TaskError(f"Error updating task: {e}")

    def delete_task(self, task_id: str) -> bool:
        """
        Delete a task by its ID.

        Args:
            task_id (str): The ID of the task to delete

        Returns:
            bool: True if task was deleted, False if not found

        Raises:
            TaskError: If delete operation fails
        """
        try:
            tasks = self._load_tasks()

            # Find and remove the task
            for i, task in enumerate(tasks):
                if task.get('task_id') == task_id:
                    del tasks[i]
                    self._save_tasks(tasks)
                    return True

            return False

        except Exception as e:
            raise TaskError(f"Error deleting task: {e}")

    def list_tasks(self, status: Optional[str] = None, priority: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List all tasks with optional filtering by status and priority.

        Args:
            status (str, optional): Filter by task status
            priority (str, optional): Filter by task priority

        Returns:
            List[Dict[str, Any]]: List of tasks matching the criteria

        Raises:
            TaskError: If listing operation fails
        """
        try:
            tasks = self._load_tasks()

            # Apply status filter if provided
            if status:
                tasks = [task for task in tasks if task.get('task_status') == status]

            # Apply priority filter if provided
            if priority:
                tasks = [task for task in tasks if task.get('task_priority') == priority]

            return tasks

        except Exception as e:
            raise TaskError(f"Error listing tasks: {e}")

    def add_tag_to_task(self, task_id: str, tag: str) -> Dict[str, Any]:
        """
        Add a tag to a specific task.

        Args:
            task_id (str): The ID of the task to add tag to
            tag (str): The tag to add

        Returns:
            Dict[str, Any]: The updated task data

        Raises:
            TaskNotFoundError: If task with given ID is not found
            TaskError: If tag addition operation fails
        """
        try:
            tasks = self._load_tasks()

            # Find the task
            for task in tasks:
                if task.get('task_id') == task_id:
                    # Add tag if not already present
                    if tag not in task.get('task_tags', []):
                        if 'task_tags' not in task:
                            task['task_tags'] = []
                        task['task_tags'].append(tag.strip().lower())
                        task['task_modified_date'] = datetime.now().isoformat()
                        self._save_tasks(tasks)
                    return task

            raise TaskNotFoundError(f"Task with ID {task_id} not found")

        except TaskNotFoundError:
            raise
        except Exception as e:
            raise TaskError(f"Error adding tag to task: {e}")

    def remove_tag_from_task(self, task_id: str, tag: str) -> Dict[str, Any]:
        """
        Remove a tag from a specific task.

        Args:
            task_id (str): The ID of the task to remove tag from
            tag (str): The tag to remove

        Returns:
            Dict[str, Any]: The updated task data

        Raises:
            TaskNotFoundError: If task with given ID is not found
            TaskError: If tag removal operation fails
        """
        try:
            tasks = self._load_tasks()

            # Find the task
            for task in tasks:
                if task.get('task_id') == task_id:
                    # Remove tag if present
                    if tag.strip().lower() in task.get('task_tags', []):
                        task['task_tags'].remove(tag.strip().lower())
                        task['task_modified_date'] = datetime.now().isoformat()
                        self._save_tasks(tasks)
                    return task

            raise TaskNotFoundError(f"Task with ID {task_id} not found")

        except TaskNotFoundError:
            raise
        except Exception as e:
            raise TaskError(f"Error removing tag from task: {e}")

    def add_comment_to_task(self, task_id: str, comment: str) -> Dict[str, Any]:
        """
        Add a comment to a specific task.

        Args:
            task_id (str): The ID of the task to add comment to
            comment (str): The comment to add

        Returns:
            Dict[str, Any]: The updated task data

        Raises:
            TaskNotFoundError: If task with given ID is not found
            TaskError: If comment addition operation fails
        """
        try:
            tasks = self._load_tasks()

            # Find the task
            for task in tasks:
                if task.get('task_id') == task_id:
                    # Add comment
                    if 'task_comments' not in task:
                        task['task_comments'] = []
                    task['task_comments'].append(comment.strip())
                    task['task_modified_date'] = datetime.now().isoformat()
                    self._save_tasks(tasks)
                    return task

            raise TaskNotFoundError(f"Task with ID {task_id} not found")

        except TaskNotFoundError:
            raise
        except Exception as e:
            raise TaskError(f"Error adding comment to task: {e}")


# Create a default instance for easy import and use
task_manager = TaskManager()

# Convenience functions for easy import
def create_task(task_name: str, task_description: str, task_due_date: str,
               task_status: str = "todo", task_priority: str = "medium",
               task_tags: List[str] = None, task_comments: List[str] = None) -> Dict[str, Any]:
    """Create a new task using the default manager"""
    return task_manager.create_task(task_name, task_description, task_due_date,
                                   task_status, task_priority, task_tags, task_comments)

def list_tasks(status: Optional[str] = None, priority: Optional[str] = None) -> List[Dict[str, Any]]:
    """List tasks using the default manager"""
    return task_manager.list_tasks(status, priority)

def get_task(task_id: str) -> Dict[str, Any]:
    """Get a task by ID using the default manager"""
    return task_manager.get_task(task_id)

def update_task(task_id: str, **kwargs) -> Dict[str, Any]:
    """Update a task using the default manager"""
    return task_manager.update_task(task_id, **kwargs)

def delete_task(task_id: str) -> bool:
    """Delete a task using the default manager"""
    return task_manager.delete_task(task_id)

def add_tag_to_task(task_id: str, tag: str) -> Dict[str, Any]:
    """Add a tag to a task using the default manager"""
    return task_manager.add_tag_to_task(task_id, tag)

def remove_tag_from_task(task_id: str, tag: str) -> Dict[str, Any]:
    """Remove a tag from a task using the default manager"""
    return task_manager.remove_tag_from_task(task_id, tag)

def add_comment_to_task(task_id: str, comment: str) -> Dict[str, Any]:
    """Add a comment to a task using the default manager"""
    return task_manager.add_comment_to_task(task_id, comment)

# Create standalone functions for easy import
task_manager = TaskManager()
create_task = task_manager.create_task
list_tasks = task_manager.list_tasks
get_task = task_manager.get_task
update_task = task_manager.update_task
delete_task = task_manager.delete_task