import json
import os
from time import time
from datetime import datetime
from typing import List, Dict, Any, Optional, Literal
from uuid import uuid4
from pydantic import BaseModel, Field

class TaskError(Exception):
    """Bazowy wyjątek dla zadań"""
    pass

class TaskNotFoundError(TaskError):
    """Zadanie nie znalezione"""
    pass

class TaskValidationError(TaskError):
    """Błąd walidacji zadania"""
    pass

class Task(BaseModel):
    task_id: str = Field(default_factory=lambda: str(uuid4()))
    task_name: str = Field(..., min_length=1, max_length=100)
    task_description: str = Field(..., min_length=1, max_length=2000)
    task_status: Literal["todo", "in_progress", "completed", "cancelled"] = "todo"
    task_priority: Literal["low", "medium", "high", "urgent"] = "medium"
    task_due_date: str = Field(..., min_length=1, max_length=100)
    task_created_date: str = Field(default_factory=lambda: datetime.now().isoformat())
    task_modified_date: str = Field(default_factory=lambda: datetime.now().isoformat())
    task_completed_date: Optional[str] = Field(default=None)
    task_tags: List[str] = Field(default_factory=list)
    task_comments: List[str] = Field(default_factory=list)

class TaskManager:
    def __init__(self, storage_path: str = "tasks.jsonl"):
        self.storage_path = storage_path
        self.tasks_cache = None
        self.cache_timestamp = 0
        self.cache_ttl = 60  # Cache ważny przez 60 sekund

    def _is_cache_valid(self) -> bool:
        """Sprawdza czy cache jest jeszcze ważny"""
        if self.tasks_cache is None:
            return False
        if not os.path.exists(self.storage_path):
            return self.cache_timestamp > time() - self.cache_ttl
        file_mtime = os.path.getmtime(self.storage_path)
        # Cache is valid if cache is recent and file has not changed since cache_timestamp
        return self.cache_timestamp > time() - self.cache_ttl and file_mtime < self.cache_timestamp

    def _load_tasks(self) -> List[Dict[str, Any]]:
        """Wczytuje zadania z cache lub pliku"""
        if self._is_cache_valid():
            return self.tasks_cache
            
        try:
            with open(self.storage_path, "r") as f:
                self.tasks_cache = [json.loads(line) for line in f.readlines()]
                self.cache_timestamp = time()
                return self.tasks_cache    
        except FileNotFoundError:
            self.tasks_cache = []
            self.cache_timestamp = time()
            return self.tasks_cache
        except json.JSONDecodeError:
            self.tasks_cache = []
            self.cache_timestamp = time()
            return self.tasks_cache
        
    def _save_tasks(self, tasks: List[Dict[str, Any]]) -> None:
        """Zapisuje całą listę zadań do pliku"""
        with open(self.storage_path, 'w') as f:
            for task in tasks:
                f.write(json.dumps(task) + '\n')
        self.tasks_cache = tasks
        self.cache_timestamp = time()

    def create_task(self, task_name: str, task_description: str, 
                   task_due_date: str, task_status: str = "todo", 
                   task_priority: str = "medium", task_tags: List[str] = None, 
                   task_comments: List[str] = None) -> Dict[str, Any]:
        """Tworzy nowe zadanie"""
        if task_tags is None:
            task_tags = []
        if task_comments is None:
            task_comments = []
        if not task_due_date:
            raise TaskValidationError("task_due_date is required and cannot be empty")
        task = Task(
            task_name=task_name,
            task_description=task_description,
            task_status=task_status,
            task_priority=task_priority,
            task_due_date=task_due_date,
            task_tags=task_tags,
            task_comments=task_comments
        )
        tasks = self._load_tasks()
        tasks.append(task.model_dump())
        self._save_tasks(tasks)
        return task.model_dump()

    def get_task(self, task_id: str) -> Dict[str, Any]:
        """Pobiera pojedyncze zadanie"""
        tasks = self._load_tasks()
        for task in tasks:
            if task['task_id'] == task_id:
                return task
        raise TaskNotFoundError(f"Task with ID {task_id} not found")

    def update_task(self, task_id: str, **kwargs) -> Dict[str, Any]:
        """Aktualizuje istniejące zadanie"""
        tasks = self._load_tasks()
        for task in tasks:
            if task['task_id'] == task_id:
                # Aktualizuj pola
                for key, value in kwargs.items():
                    if key in task:
                        task[key] = value
                
                # Zawsze aktualizuj datę modyfikacji
                task['task_modified_date'] = datetime.now().isoformat()
                
                # Jeśli status to "completed", ustaw datę ukończenia
                if task.get('task_status') == 'completed' and not task.get('task_completed_date'):
                    task['task_completed_date'] = datetime.now().isoformat()
                
                self._save_tasks(tasks)
                return task
        raise TaskNotFoundError(f"Task with ID {task_id} not found")

    def delete_task(self, task_id: str) -> bool:
        """Usuwa zadanie"""
        tasks = self._load_tasks()
        for i, task in enumerate(tasks):
            if task['task_id'] == task_id:
                del tasks[i]
                self._save_tasks(tasks)
                return True
        return False

    def list_tasks(self, status: Optional[str] = None, priority: Optional[str] = None) -> List[Dict[str, Any]]:
        """Listuje zadania z opcjonalnym filtrowaniem"""
        tasks = self._load_tasks()
        
        if status:
            tasks = [task for task in tasks if task.get('task_status') == status]
        
        if priority:
            tasks = [task for task in tasks if task.get('task_priority') == priority]
        
        return tasks

    def add_tag_to_task(self, task_id: str, tag: str) -> Dict[str, Any]:
        """Dodaje tag do zadania"""
        tasks = self._load_tasks()
        for task in tasks:
            if task['task_id'] == task_id:
                if tag not in task['task_tags']:
                    task['task_tags'].append(tag)
                    task['task_modified_date'] = datetime.now().isoformat()
                    self._save_tasks(tasks)
                return task
        raise TaskNotFoundError(f"Task with ID {task_id} not found")

    def remove_tag_from_task(self, task_id: str, tag: str) -> Dict[str, Any]:
        """Usuwa tag z zadania"""
        tasks = self._load_tasks()
        for task in tasks:
            if task['task_id'] == task_id:
                if tag in task['task_tags']:
                    task['task_tags'].remove(tag)
                    task['task_modified_date'] = datetime.now().isoformat()
                    self._save_tasks(tasks)
                return task
        raise TaskNotFoundError(f"Task with ID {task_id} not found")

# Create standalone functions for easy import
task_manager = TaskManager()
create_task = task_manager.create_task
list_tasks = task_manager.list_tasks
get_task = task_manager.get_task
update_task = task_manager.update_task
delete_task = task_manager.delete_task