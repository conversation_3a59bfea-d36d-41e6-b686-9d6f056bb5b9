"""# Funkcja pomocnicza do wczytywania notatek
def _load_notes():
    Wczytuje notatki z pliku i obsługuje błędy.
    try:
        with open("notes.jsonl", "r") as f:
            return [json.loads(line) for line in f.readlines()]
    except FileNotFoundError:
        return []
    except json.JSONDecodeError:
        return []

@Kirk.tool_plain
def write_note(content: str) -> str:
    Used to write a note in json format.
        "id": random.getrandbits(32),
        "content": content,
        "timestamp": datetime.now().isoformat(),
        "title": content.split('\n')[0]  #use first line as title
    }
    with open('notes.jsonl', 'a') as f:
        f.write(json.dumps(note) + '\n')
    return f"Note written: {note['title']}"

@Kirk.tool_plain
def list_notes() -> str:
    used to list all notes
    notes = _load_notes()
    if not notes:
        return "No notes found."
    return "\n".join([f"{note['id']}: {note['title']}" for note in notes])

@Kirk.tool_plain
def search_notes(query: str) -> str:
    used to search notes
    notes = _load_notes()
    if not notes:
        return "No notes found."
    
    filtered_notes = [note for note in notes if query in note['content']]
    if not filtered_notes:
        return "No notes found matching your query."
    return "\n".join([f"{note['id']}: {note['title']}" for note in filtered_notes])

@Kirk.tool_plain  # Poprawiono literówkę z @kirk na @Kirk
def read_note(note_id: int) -> str:
    used to read a note
    notes = _load_notes()
    if not notes:
        return "No notes found."
    
    for note in notes:
        if note['id'] == note_id:
            return note['content']
    return "Note not found."
"""

import json
from datetime import datetime
from typing import List, Dict, Any
from uuid import uuid4
from pydantic import BaseModel, Field, ValidationError
from models import NoteBase, NoteCreate, NoteUpdate, NoteResponse

class NoteManager:
    def __init__(self, storage_path: str = "notes.jsonl"):
        self.storage_path = storage_path
        self.notes_cache = None

    def _load_notes(self) -> List[Dict[str, Any]]:
        """Reads notes from cache or file"""
        if self.notes_cache is not None:
            return self.notes_cache
        
        try:
            with open(self.storage_path, "r") as f:
                self.notes_cache = [json.loads(line) for line in f.readlines()]
                return self.notes_cache
        except FileNotFoundError:
            self.notes_cache = []
            return self.notes_cache
        except json.JSONDecodeError:
            self.notes_cache = []
            return self.notes_cache
    
    def _invalidate_cache(self):
        """Clears the cache"""
        self.notes_cache = None

    def write_note(self, content: str, title: str = None) -> : 
        """Writes a note to the file""" 
        note = {
            "id": uuid4().hex , #gen random id jsonl valid #TypeError: Object of type UUID is not JSON serializable need to fix error#error solved by using uuid4().hex instead of uuid4()
            "content": content,    #content,
            "timestamp": datetime.now().isoformat(),
            "title": content.split('\n')[0]  #use first line as title
        }
        try:
        
            with open(self.storage_path, 'a') as f: #append to file
                f.write(json.dumps(note) + '\n') #write note to file
            self._invalidate_cache()#clear cache
            return note
        except Exception as e:
            raise e    
    
    def list_notes(self) -> List[Dict[str, Any]]:
        """Lists all notes"""
        notes = self._load_notes() #load notes
        if not notes:
            return "No notes found."
        return notes   

    def search_notes(self, query: str) -> List[Dict[str, Any]]:
        """Searches for notes"""
        notes = self._load_notes() #load notes
        filtered_notes = [note for note in notes if query in note['content']] #search notes
        return filtered_notes
    
    def read_note(self, note_id: str) -> Dict[str, Any]:
        """Reads a note"""
        notes = self._load_notes() #load notes
        for note in notes:
            if note['id'] == note_id:
                return note #return note
        return None    
    
