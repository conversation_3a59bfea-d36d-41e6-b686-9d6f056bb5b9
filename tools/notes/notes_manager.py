"""
Notes Manager Module

This module provides a comprehensive note management system with consistent architecture
using centralized models from models.py. It handles CRUD operations for notes with
proper validation, error handling, and caching mechanisms.

Features:
- Create, read, update, delete notes
- Search functionality
- Caching for performance
- Consistent data validation using Pydantic models
- Comprehensive error handling
"""

import json
import os
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import ValidationError
from models import NoteBase, NoteCreate, NoteUpdate, NoteResponse


# Custom exception classes for different note errors
class NoteError(Exception):
    """Base exception for note operations"""
    pass


class NoteNotFoundError(NoteError):
    """Exception raised when a note is not found"""
    pass


class NoteValidationError(NoteError):
    """Exception raised for note validation errors"""
    pass


class NoteFileError(NoteError):
    """Exception raised for file operation errors"""
    pass


class NoteManager:
    """
    Comprehensive note management system with consistent architecture.

    This class handles all CRUD operations for notes using centralized models
    from models.py. It provides caching, validation, and error handling.

    Attributes:
        storage_path (str): Path to the notes storage file
        notes_cache (List[Dict]): Cached notes for performance
        cache_timestamp (float): Timestamp of last cache update
        cache_ttl (int): Cache time-to-live in seconds
    """

    def __init__(self, storage_path: str = "notes.jsonl"):
        """
        Initialize the NoteManager with storage configuration.

        Args:
            storage_path (str): Path to the JSONL file for storing notes
        """
        self.storage_path = storage_path
        self.notes_cache = None
        self.cache_timestamp = 0
        self.cache_ttl = 60  # Cache valid for 60 seconds

    def _is_cache_valid(self) -> bool:
        """
        Check if the cache is still valid based on file modification time and TTL.

        Returns:
            bool: True if cache is valid, False otherwise
        """
        if self.notes_cache is None:
            return False
        if not os.path.exists(self.storage_path):
            return self.cache_timestamp > time.time() - self.cache_ttl
        file_mtime = os.path.getmtime(self.storage_path)
        return (self.cache_timestamp > time.time() - self.cache_ttl and
                file_mtime < self.cache_timestamp)

    def _load_notes(self) -> List[Dict[str, Any]]:
        """
        Load notes from cache or file with proper error handling.

        Returns:
            List[Dict[str, Any]]: List of note dictionaries

        Raises:
            NoteFileError: If file operations fail
        """
        if self._is_cache_valid():
            return self.notes_cache

        try:
            with open(self.storage_path, "r") as f:
                content = f.read().strip()
                if not content:
                    # Empty file is valid, return empty list
                    self.notes_cache = []
                    self.cache_timestamp = time.time()
                    return self.notes_cache

                # Parse each line that has content
                self.notes_cache = []
                for line in content.split('\n'):
                    line = line.strip()
                    if line:
                        try:
                            self.notes_cache.append(json.loads(line))
                        except json.JSONDecodeError as e:
                            # Skip malformed lines but continue processing
                            print(f"Warning: Skipping malformed line in notes file: {line[:50]}...")
                            continue

                self.cache_timestamp = time.time()
                return self.notes_cache
        except FileNotFoundError:
            self.notes_cache = []
            self.cache_timestamp = time.time()
            return self.notes_cache
        except json.JSONDecodeError as e:
            raise NoteFileError(f"Error decoding notes file: {e}")
        except Exception as e:
            raise NoteFileError(f"Error loading notes: {e}")

    def _save_notes(self, notes: List[Dict[str, Any]]) -> None:
        """
        Save notes to file and update cache.

        Args:
            notes (List[Dict[str, Any]]): List of note dictionaries to save

        Raises:
            NoteFileError: If file operations fail
        """
        try:
            with open(self.storage_path, 'w') as f:
                for note in notes:
                    f.write(json.dumps(note) + '\n')
            self.notes_cache = notes
            self.cache_timestamp = time.time()
        except Exception as e:
            raise NoteFileError(f"Error saving notes: {e}")

    def _invalidate_cache(self):
        """Clear the cache to force reload from file"""
        self.notes_cache = None

    def create_note(self, content: str, title: str = None) -> Dict[str, Any]:
        """
        Create a new note with validation and proper error handling.

        Args:
            content (str): The content of the note
            title (str, optional): The title of the note. If not provided,
                                 uses the first line of content

        Returns:
            Dict[str, Any]: The created note data

        Raises:
            NoteValidationError: If note data is invalid
            NoteFileError: If file operations fail
        """
        try:
            # Create note using the centralized model
            note_data = NoteCreate(
                content=content,
                title=title or content.split('\n')[0] if content else "Untitled"
            )

            # Load existing notes and add the new one
            notes = self._load_notes()
            note_dict = note_data.model_dump()
            notes.append(note_dict)

            # Save all notes back to file
            self._save_notes(notes)

            return note_dict

        except ValidationError as e:
            raise NoteValidationError(f"Invalid note data: {e}")
        except Exception as e:
            raise NoteError(f"Error creating note: {e}")

    # Alias for backward compatibility
    def write_note(self, content: str, title: str = None) -> Dict[str, Any]:
        """Alias for create_note method for backward compatibility"""
        return self.create_note(content, title)
    
    def list_notes(self) -> List[Dict[str, Any]]:
        """
        List all notes with proper error handling.

        Returns:
            List[Dict[str, Any]]: List of all notes

        Raises:
            NoteFileError: If file operations fail
        """
        try:
            notes = self._load_notes()
            return notes
        except Exception as e:
            raise NoteError(f"Error listing notes: {e}")

    def search_notes(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for notes containing the specified query string.

        Args:
            query (str): Search query string

        Returns:
            List[Dict[str, Any]]: List of notes matching the query

        Raises:
            NoteError: If search operation fails
        """
        try:
            notes = self._load_notes()
            # Search in both content and title
            filtered_notes = [
                note for note in notes
                if query.lower() in note.get('content', '').lower() or
                   query.lower() in note.get('title', '').lower()
            ]
            return filtered_notes
        except Exception as e:
            raise NoteError(f"Error searching notes: {e}")

    def get_note(self, note_id: str) -> Dict[str, Any]:
        """
        Get a specific note by its ID.

        Args:
            note_id (str): The ID of the note to retrieve

        Returns:
            Dict[str, Any]: The note data

        Raises:
            NoteNotFoundError: If note with given ID is not found
            NoteError: If retrieval operation fails
        """
        try:
            notes = self._load_notes()
            for note in notes:
                if note.get('id') == note_id:
                    return note
            raise NoteNotFoundError(f"Note with ID {note_id} not found")
        except NoteNotFoundError:
            raise
        except Exception as e:
            raise NoteError(f"Error retrieving note: {e}")

    # Alias for backward compatibility
    def read_note(self, note_id: str) -> Dict[str, Any]:
        """Alias for get_note method for backward compatibility"""
        return self.get_note(note_id)

    def update_note(self, note_id: str, content: str = None, title: str = None) -> Dict[str, Any]:
        """
        Update an existing note with new content or title.

        Args:
            note_id (str): The ID of the note to update
            content (str, optional): New content for the note
            title (str, optional): New title for the note

        Returns:
            Dict[str, Any]: The updated note data

        Raises:
            NoteNotFoundError: If note with given ID is not found
            NoteValidationError: If update data is invalid
            NoteError: If update operation fails
        """
        try:
            notes = self._load_notes()

            # Find the note to update
            note_index = None
            for i, note in enumerate(notes):
                if note.get('id') == note_id:
                    note_index = i
                    break

            if note_index is None:
                raise NoteNotFoundError(f"Note with ID {note_id} not found")

            # Prepare update data
            update_data = {}
            if content is not None:
                update_data['content'] = content
            if title is not None:
                update_data['title'] = title

            # Validate update data using the model
            if update_data:
                try:
                    NoteUpdate(**update_data)
                except ValidationError as e:
                    raise NoteValidationError(f"Invalid update data: {e}")

                # Apply updates
                notes[note_index].update(update_data)

                # Save updated notes
                self._save_notes(notes)

            return notes[note_index]

        except (NoteNotFoundError, NoteValidationError):
            raise
        except Exception as e:
            raise NoteError(f"Error updating note: {e}")

    def delete_note(self, note_id: str) -> bool:
        """
        Delete a note by its ID.

        Args:
            note_id (str): The ID of the note to delete

        Returns:
            bool: True if note was deleted, False if not found

        Raises:
            NoteError: If delete operation fails
        """
        try:
            notes = self._load_notes()

            # Find and remove the note
            for i, note in enumerate(notes):
                if note.get('id') == note_id:
                    del notes[i]
                    self._save_notes(notes)
                    return True

            return False

        except Exception as e:
            raise NoteError(f"Error deleting note: {e}")


# Create a default instance for easy import and use
note_manager = NoteManager()

# Convenience functions for easy import
def create_note(content: str, title: str = None) -> Dict[str, Any]:
    """Create a new note using the default manager"""
    return note_manager.create_note(content, title)

def list_notes() -> List[Dict[str, Any]]:
    """List all notes using the default manager"""
    return note_manager.list_notes()

def search_notes(query: str) -> List[Dict[str, Any]]:
    """Search notes using the default manager"""
    return note_manager.search_notes(query)

def get_note(note_id: str) -> Dict[str, Any]:
    """Get a note by ID using the default manager"""
    return note_manager.get_note(note_id)

def update_note(note_id: str, content: str = None, title: str = None) -> Dict[str, Any]:
    """Update a note using the default manager"""
    return note_manager.update_note(note_id, content, title)

def delete_note(note_id: str) -> bool:
    """Delete a note using the default manager"""
    return note_manager.delete_note(note_id)

# Backward compatibility aliases
write_note = create_note
read_note = get_note
    
