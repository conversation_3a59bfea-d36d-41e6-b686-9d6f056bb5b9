#!/usr/bin/env python3
"""
Comprehensive Test Runner for All Tool Managers

This script runs all tests for the notes, tasks, and reminder managers
to validate that the consistent models.py architecture is working correctly
across all components.

Usage:
    python test_all_managers.py

Expected Output:
    All tests should pass, demonstrating that the refactored managers
    work correctly with the centralized models.py architecture.
"""

import sys
import os
import subprocess
import tempfile
import shutil
from datetime import datetime

def run_test_script(script_name):
    """
    Run a test script and return the results.
    
    Args:
        script_name (str): Name of the test script to run
        
    Returns:
        tuple: (success: bool, output: str, error: str)
    """
    try:
        print(f"\n{'='*60}")
        print(f"Running {script_name}")
        print(f"{'='*60}")
        
        # Run the test script
        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            timeout=60  # 60 second timeout
        )
        
        # Print the output in real-time style
        if result.stdout:
            print(result.stdout)
        
        if result.stderr and result.returncode != 0:
            print("STDERR:")
            print(result.stderr)
        
        return result.returncode == 0, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        print(f"✗ {script_name} timed out after 60 seconds")
        return False, "", "Timeout"
    except Exception as e:
        print(f"✗ Failed to run {script_name}: {e}")
        return False, "", str(e)


def test_models_import():
    """Test that models.py can be imported and used correctly"""
    print("\n=== Testing Models.py Import and Basic Usage ===")
    
    try:
        # Test importing models
        from models import (
            NoteBase, NoteCreate, NoteUpdate, NoteResponse,
            TaskBase, TaskCreate, TaskUpdate, TaskResponse,
            ReminderBase, ReminderCreate, ReminderUpdate, ReminderResponse,
            ErrorResponse, SuccessResponse, PaginatedResponse
        )
        print("✓ Successfully imported all model classes")
        
        # Test creating model instances
        note = NoteCreate(content="Test note", title="Test Title")
        print(f"✓ Created NoteCreate instance: {note.title}")
        
        task = TaskCreate(
            task_name="Test Task",
            task_description="Test Description",
            task_due_date="2025-12-31"
        )
        print(f"✓ Created TaskCreate instance: {task.task_name}")
        
        reminder = ReminderCreate(
            reminder_name="Test Reminder",
            reminder_description="Test Description",
            reminder_date="2025-12-31",
            reminder_time="12:00"
        )
        print(f"✓ Created ReminderCreate instance: {reminder.reminder_name}")
        
        # Test response models
        error = ErrorResponse(error="Test error", detail="Test detail")
        success = SuccessResponse(message="Test success", data={"key": "value"})
        print("✓ Created response model instances")
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to test models: {e}")
        return False


def test_architecture_consistency():
    """Test that all managers follow the same architectural patterns"""
    print("\n=== Testing Architecture Consistency ===")
    
    try:
        # Test that all managers have the same basic interface
        from tools.notes.notes_manager import NoteManager
        from tools.tasks.task_manager import TaskManager  
        from tools.reminder.reminder_manager import ReminderManager
        
        print("✓ All manager classes imported successfully")
        
        # Test that all managers have similar methods
        note_manager = NoteManager()
        task_manager = TaskManager()
        reminder_manager = ReminderManager()
        
        # Check for common CRUD methods
        managers = [
            ("NoteManager", note_manager, ["create_note", "list_notes", "get_note", "update_note", "delete_note"]),
            ("TaskManager", task_manager, ["create_task", "list_tasks", "get_task", "update_task", "delete_task"]),
            ("ReminderManager", reminder_manager, ["create_reminder", "list_reminders", "get_reminder", "update_reminder", "delete_reminder"])
        ]
        
        for manager_name, manager_instance, expected_methods in managers:
            for method_name in expected_methods:
                if hasattr(manager_instance, method_name):
                    print(f"✓ {manager_name} has {method_name} method")
                else:
                    print(f"✗ {manager_name} missing {method_name} method")
                    return False
        
        # Test that all managers have caching
        cache_attributes = ["cache_timestamp", "cache_ttl"]
        for manager_name, manager_instance, _ in managers:
            for attr_name in cache_attributes:
                if hasattr(manager_instance, attr_name):
                    print(f"✓ {manager_name} has {attr_name} attribute")
                else:
                    print(f"✗ {manager_name} missing {attr_name} attribute")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to test architecture consistency: {e}")
        return False


def cleanup_test_files():
    """Clean up any test files created during testing"""
    print("\n=== Cleaning Up Test Files ===")
    
    test_files = [
        "notes.jsonl",
        "tasks.jsonl", 
        "reminders.jsonl",
        "test_notes.jsonl",
        "test_tasks.jsonl",
        "test_reminders.jsonl"
    ]
    
    cleaned = 0
    for file_name in test_files:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ Removed {file_name}")
                cleaned += 1
            except Exception as e:
                print(f"! Could not remove {file_name}: {e}")
    
    if cleaned == 0:
        print("✓ No test files to clean up")
    else:
        print(f"✓ Cleaned up {cleaned} test files")


def main():
    """Main test runner function"""
    print("🚀 Starting Comprehensive Tool Manager Tests")
    print("=" * 80)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # Track overall results
    total_tests = 0
    passed_tests = 0
    
    # Test 1: Models import and basic usage
    total_tests += 1
    if test_models_import():
        passed_tests += 1
    
    # Test 2: Architecture consistency
    total_tests += 1
    if test_architecture_consistency():
        passed_tests += 1
    
    # Test 3: Individual manager tests
    test_scripts = [
        "test_notes_manager.py",
        "test_task_manager.py", 
        "test_reminder_manager.py"
    ]
    
    for script in test_scripts:
        total_tests += 1
        if os.path.exists(script):
            success, output, error = run_test_script(script)
            if success:
                passed_tests += 1
            else:
                print(f"✗ {script} failed")
        else:
            print(f"! {script} not found, skipping")
    
    # Clean up test files
    cleanup_test_files()
    
    # Final results
    print("\n" + "=" * 80)
    print("🏁 COMPREHENSIVE TEST RESULTS")
    print("=" * 80)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The consistent models.py architecture is working correctly across all managers.")
        print("✅ All tool managers (notes, tasks, reminders) are functioning properly.")
        print("✅ Data validation, error handling, and CRUD operations are working as expected.")
        return True
    else:
        print(f"\n❌ {total_tests - passed_tests} TEST(S) FAILED")
        print("Please review the failed tests and fix any issues before proceeding.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
