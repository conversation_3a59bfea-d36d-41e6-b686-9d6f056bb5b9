#!/usr/bin/env python3
"""
Test Script for Notes Manager

This script validates that the notes manager works correctly with the new
models.py architecture. It tests all CRUD operations, error handling,
and data validation.

Usage:
    python test_notes_manager.py

Expected Output:
    All tests should pass with detailed output showing the operations
    and their results.
"""

import sys
import os
import json
import tempfile
from datetime import datetime
from typing import Dict, Any, List

# Add the tools directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tools', 'notes'))

try:
    from tools.notes.notes_manager import (
        NoteManager, 
        create_note, 
        list_notes, 
        search_notes, 
        get_note, 
        update_note, 
        delete_note,
        NoteError,
        NoteNotFoundError,
        NoteValidationError,
        NoteFileError
    )
    print("✓ Successfully imported notes manager components")
except ImportError as e:
    print(f"✗ Failed to import notes manager: {e}")
    sys.exit(1)


def test_note_creation():
    """Test creating notes with various inputs"""
    print("\n=== Testing Note Creation ===")
    
    # Test basic note creation
    try:
        note1 = create_note("This is my first test note", "Test Note 1")
        print(f"✓ Created note: {note1['title']}")
        assert 'id' in note1
        assert 'timestamp' in note1
        assert note1['content'] == "This is my first test note"
        assert note1['title'] == "Test Note 1"
        print(f"  - ID: {note1['id']}")
        print(f"  - Timestamp: {note1['timestamp']}")
    except Exception as e:
        print(f"✗ Failed to create basic note: {e}")
        return False
    
    # Test note creation without title (should use first line)
    try:
        note2 = create_note("Auto Title Note\nThis should use the first line as title")
        print(f"✓ Created note with auto title: {note2['title']}")
        assert note2['title'] == "Auto Title Note"
    except Exception as e:
        print(f"✗ Failed to create note with auto title: {e}")
        return False
    
    # Test note creation with empty content (should fail)
    try:
        create_note("")
        print("✗ Should have failed with empty content")
        return False
    except Exception as e:
        print(f"✓ Correctly rejected empty content: {type(e).__name__}")
    
    return True


def test_note_listing():
    """Test listing all notes"""
    print("\n=== Testing Note Listing ===")
    
    try:
        notes = list_notes()
        print(f"✓ Listed {len(notes)} notes")
        
        for i, note in enumerate(notes[:3]):  # Show first 3 notes
            print(f"  {i+1}. {note['title']} (ID: {note['id'][:8]}...)")
        
        if len(notes) > 3:
            print(f"  ... and {len(notes) - 3} more notes")
            
        return True
    except Exception as e:
        print(f"✗ Failed to list notes: {e}")
        return False


def test_note_search():
    """Test searching for notes"""
    print("\n=== Testing Note Search ===")
    
    # Create a note with specific content for searching
    try:
        search_note = create_note("This note contains the word SEARCHABLE for testing", "Search Test")
        print(f"✓ Created search test note: {search_note['id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create search test note: {e}")
        return False
    
    # Test searching
    try:
        results = search_notes("SEARCHABLE")
        print(f"✓ Search found {len(results)} notes")
        
        found_our_note = any(note['id'] == search_note['id'] for note in results)
        if found_our_note:
            print("✓ Found our test note in search results")
        else:
            print("✗ Did not find our test note in search results")
            return False
            
        # Test search with no results
        no_results = search_notes("NONEXISTENT_SEARCH_TERM_12345")
        print(f"✓ Search with no matches returned {len(no_results)} results")
        
        return True
    except Exception as e:
        print(f"✗ Failed to search notes: {e}")
        return False


def test_note_retrieval():
    """Test getting specific notes by ID"""
    print("\n=== Testing Note Retrieval ===")
    
    # Get all notes and test retrieving the first one
    try:
        notes = list_notes()
        if not notes:
            print("! No notes available for retrieval test")
            return True
            
        first_note_id = notes[0]['id']
        retrieved_note = get_note(first_note_id)
        
        print(f"✓ Retrieved note: {retrieved_note['title']}")
        assert retrieved_note['id'] == first_note_id
        assert 'content' in retrieved_note
        assert 'timestamp' in retrieved_note
        
        # Test retrieving non-existent note
        try:
            get_note("nonexistent_id_12345")
            print("✗ Should have failed with non-existent ID")
            return False
        except NoteNotFoundError:
            print("✓ Correctly handled non-existent note ID")
        
        return True
    except Exception as e:
        print(f"✗ Failed to retrieve note: {e}")
        return False


def test_note_update():
    """Test updating notes"""
    print("\n=== Testing Note Updates ===")
    
    # Create a note to update
    try:
        original_note = create_note("Original content", "Original Title")
        print(f"✓ Created note for update test: {original_note['id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create note for update: {e}")
        return False
    
    # Test updating content only
    try:
        updated_note = update_note(original_note['id'], content="Updated content")
        print("✓ Updated note content")
        assert updated_note['content'] == "Updated content"
        assert updated_note['title'] == "Original Title"  # Should remain unchanged
    except Exception as e:
        print(f"✗ Failed to update note content: {e}")
        return False
    
    # Test updating title only
    try:
        updated_note = update_note(original_note['id'], title="Updated Title")
        print("✓ Updated note title")
        assert updated_note['title'] == "Updated Title"
        assert updated_note['content'] == "Updated content"  # Should remain from previous update
    except Exception as e:
        print(f"✗ Failed to update note title: {e}")
        return False
    
    # Test updating both content and title
    try:
        updated_note = update_note(original_note['id'], 
                                 content="Final content", 
                                 title="Final Title")
        print("✓ Updated both content and title")
        assert updated_note['content'] == "Final content"
        assert updated_note['title'] == "Final Title"
    except Exception as e:
        print(f"✗ Failed to update both fields: {e}")
        return False
    
    return True


def test_note_deletion():
    """Test deleting notes"""
    print("\n=== Testing Note Deletion ===")
    
    # Create a note to delete
    try:
        note_to_delete = create_note("This note will be deleted", "Delete Test")
        print(f"✓ Created note for deletion test: {note_to_delete['id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create note for deletion: {e}")
        return False
    
    # Test successful deletion
    try:
        result = delete_note(note_to_delete['id'])
        print(f"✓ Deleted note: {result}")
        assert result == True
    except Exception as e:
        print(f"✗ Failed to delete note: {e}")
        return False
    
    # Verify note is gone
    try:
        get_note(note_to_delete['id'])
        print("✗ Note should have been deleted but still exists")
        return False
    except NoteNotFoundError:
        print("✓ Confirmed note was deleted")
    
    # Test deleting non-existent note
    try:
        result = delete_note("nonexistent_id_12345")
        print(f"✓ Deletion of non-existent note returned: {result}")
        assert result == False
    except Exception as e:
        print(f"✗ Unexpected error deleting non-existent note: {e}")
        return False
    
    return True


def run_all_tests():
    """Run all note manager tests"""
    print("Starting Notes Manager Tests")
    print("=" * 50)
    
    tests = [
        test_note_creation,
        test_note_listing,
        test_note_search,
        test_note_retrieval,
        test_note_update,
        test_note_deletion
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Notes Manager Tests Complete: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Notes manager is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
