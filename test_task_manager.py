#!/usr/bin/env python3
"""
Test Script for Task Manager

This script validates that the task manager works correctly with the new
models.py architecture. It tests all CRUD operations, error handling,
and data validation.

Usage:
    python test_task_manager.py

Expected Output:
    All tests should pass with detailed output showing the operations
    and their results.
"""

import sys
import os
from datetime import datetime, date
from typing import Dict, Any, List

# Add the tools directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tools', 'tasks'))

try:
    from tools.tasks.task_manager import (
        TaskManager,
        create_task,
        list_tasks,
        get_task,
        update_task,
        delete_task,
        add_tag_to_task,
        remove_tag_from_task,
        add_comment_to_task,
        TaskError,
        TaskNotFoundError,
        TaskValidationError,
        TaskFileError
    )
    print("✓ Successfully imported task manager components")
except ImportError as e:
    print(f"✗ Failed to import task manager: {e}")
    sys.exit(1)


def test_task_creation():
    """Test creating tasks with various inputs"""
    print("\n=== Testing Task Creation ===")
    
    # Test basic task creation
    try:
        task1 = create_task(
            task_name="Test Task 1",
            task_description="This is a test task for validation",
            task_due_date="2025-12-31",
            task_priority="high",
            task_tags=["test", "validation"],
            task_comments=["Initial comment"]
        )
        print(f"✓ Created task: {task1['task_name']}")
        assert 'task_id' in task1
        assert 'task_created_date' in task1
        assert task1['task_status'] == "todo"
        assert task1['task_priority'] == "high"
        assert "test" in task1['task_tags']
        print(f"  - ID: {task1['task_id']}")
        print(f"  - Status: {task1['task_status']}")
        print(f"  - Priority: {task1['task_priority']}")
    except Exception as e:
        print(f"✗ Failed to create basic task: {e}")
        return False
    
    # Test task creation with minimal fields
    try:
        task2 = create_task(
            task_name="Minimal Task",
            task_description="Task with only required fields",
            task_due_date="2025-12-25"
        )
        print(f"✓ Created minimal task: {task2['task_name']}")
        assert task2['task_status'] == "todo"  # Default
        assert task2['task_priority'] == "medium"  # Default
        assert task2['task_tags'] == []  # Default empty
    except Exception as e:
        print(f"✗ Failed to create minimal task: {e}")
        return False
    
    # Test task creation with empty name (should fail)
    try:
        create_task(
            task_name="",
            task_description="This should fail",
            task_due_date="2025-12-31"
        )
        print("✗ Should have failed with empty name")
        return False
    except Exception as e:
        print(f"✓ Correctly rejected empty task name: {type(e).__name__}")
    
    # Test task creation with empty due date (should fail)
    try:
        create_task(
            task_name="Test Task",
            task_description="This should fail",
            task_due_date=""
        )
        print("✗ Should have failed with empty due date")
        return False
    except Exception as e:
        print(f"✓ Correctly rejected empty due date: {type(e).__name__}")
    
    return True


def test_task_listing():
    """Test listing tasks with filters"""
    print("\n=== Testing Task Listing ===")
    
    try:
        # List all tasks
        all_tasks = list_tasks()
        print(f"✓ Listed {len(all_tasks)} total tasks")
        
        # List tasks by status
        todo_tasks = list_tasks(status="todo")
        print(f"✓ Listed {len(todo_tasks)} todo tasks")
        
        # List tasks by priority
        high_priority_tasks = list_tasks(priority="high")
        print(f"✓ Listed {len(high_priority_tasks)} high priority tasks")
        
        # List tasks by both status and priority
        filtered_tasks = list_tasks(status="todo", priority="high")
        print(f"✓ Listed {len(filtered_tasks)} todo high priority tasks")
        
        # Show sample tasks
        for i, task in enumerate(all_tasks[:3]):
            print(f"  {i+1}. {task['task_name']} ({task['task_status']}, {task['task_priority']})")
        
        if len(all_tasks) > 3:
            print(f"  ... and {len(all_tasks) - 3} more tasks")
            
        return True
    except Exception as e:
        print(f"✗ Failed to list tasks: {e}")
        return False


def test_task_retrieval():
    """Test getting specific tasks by ID"""
    print("\n=== Testing Task Retrieval ===")
    
    try:
        tasks = list_tasks()
        if not tasks:
            print("! No tasks available for retrieval test")
            return True
            
        first_task_id = tasks[0]['task_id']
        retrieved_task = get_task(first_task_id)
        
        print(f"✓ Retrieved task: {retrieved_task['task_name']}")
        assert retrieved_task['task_id'] == first_task_id
        assert 'task_description' in retrieved_task
        assert 'task_created_date' in retrieved_task
        
        # Test retrieving non-existent task
        try:
            get_task("nonexistent_id_12345")
            print("✗ Should have failed with non-existent ID")
            return False
        except TaskNotFoundError:
            print("✓ Correctly handled non-existent task ID")
        
        return True
    except Exception as e:
        print(f"✗ Failed to retrieve task: {e}")
        return False


def test_task_update():
    """Test updating tasks"""
    print("\n=== Testing Task Updates ===")
    
    # Create a task to update
    try:
        original_task = create_task(
            task_name="Update Test Task",
            task_description="This task will be updated",
            task_due_date="2025-12-31"
        )
        print(f"✓ Created task for update test: {original_task['task_id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create task for update: {e}")
        return False
    
    # Test updating status
    try:
        updated_task = update_task(original_task['task_id'], task_status="in_progress")
        print("✓ Updated task status to in_progress")
        assert updated_task['task_status'] == "in_progress"
        assert 'task_modified_date' in updated_task
    except Exception as e:
        print(f"✗ Failed to update task status: {e}")
        return False
    
    # Test updating priority
    try:
        updated_task = update_task(original_task['task_id'], task_priority="urgent")
        print("✓ Updated task priority to urgent")
        assert updated_task['task_priority'] == "urgent"
    except Exception as e:
        print(f"✗ Failed to update task priority: {e}")
        return False
    
    # Test completing task (should set completion date)
    try:
        updated_task = update_task(original_task['task_id'], task_status="completed")
        print("✓ Completed task")
        assert updated_task['task_status'] == "completed"
        assert updated_task['task_completed_date'] is not None
        print(f"  - Completion date: {updated_task['task_completed_date']}")
    except Exception as e:
        print(f"✗ Failed to complete task: {e}")
        return False
    
    return True


def test_tag_management():
    """Test adding and removing tags"""
    print("\n=== Testing Tag Management ===")
    
    # Create a task for tag testing
    try:
        task = create_task(
            task_name="Tag Test Task",
            task_description="This task will test tag management",
            task_due_date="2025-12-31"
        )
        print(f"✓ Created task for tag test: {task['task_id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create task for tag test: {e}")
        return False
    
    # Test adding tags
    try:
        updated_task = add_tag_to_task(task['task_id'], "important")
        print("✓ Added tag 'important'")
        assert "important" in updated_task['task_tags']
        
        updated_task = add_tag_to_task(task['task_id'], "urgent")
        print("✓ Added tag 'urgent'")
        assert "urgent" in updated_task['task_tags']
        assert len(updated_task['task_tags']) == 2
    except Exception as e:
        print(f"✗ Failed to add tags: {e}")
        return False
    
    # Test removing tags
    try:
        updated_task = remove_tag_from_task(task['task_id'], "important")
        print("✓ Removed tag 'important'")
        assert "important" not in updated_task['task_tags']
        assert "urgent" in updated_task['task_tags']
        assert len(updated_task['task_tags']) == 1
    except Exception as e:
        print(f"✗ Failed to remove tag: {e}")
        return False
    
    return True


def test_comment_management():
    """Test adding comments to tasks"""
    print("\n=== Testing Comment Management ===")
    
    # Create a task for comment testing
    try:
        task = create_task(
            task_name="Comment Test Task",
            task_description="This task will test comment management",
            task_due_date="2025-12-31"
        )
        print(f"✓ Created task for comment test: {task['task_id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create task for comment test: {e}")
        return False
    
    # Test adding comments
    try:
        updated_task = add_comment_to_task(task['task_id'], "First comment")
        print("✓ Added first comment")
        assert "First comment" in updated_task['task_comments']
        
        updated_task = add_comment_to_task(task['task_id'], "Second comment")
        print("✓ Added second comment")
        assert "Second comment" in updated_task['task_comments']
        assert len(updated_task['task_comments']) == 2
        
        print(f"  - Comments: {updated_task['task_comments']}")
    except Exception as e:
        print(f"✗ Failed to add comments: {e}")
        return False
    
    return True


def test_task_deletion():
    """Test deleting tasks"""
    print("\n=== Testing Task Deletion ===")
    
    # Create a task to delete
    try:
        task_to_delete = create_task(
            task_name="Delete Test Task",
            task_description="This task will be deleted",
            task_due_date="2025-12-31"
        )
        print(f"✓ Created task for deletion test: {task_to_delete['task_id'][:8]}...")
    except Exception as e:
        print(f"✗ Failed to create task for deletion: {e}")
        return False
    
    # Test successful deletion
    try:
        result = delete_task(task_to_delete['task_id'])
        print(f"✓ Deleted task: {result}")
        assert result == True
    except Exception as e:
        print(f"✗ Failed to delete task: {e}")
        return False
    
    # Verify task is gone
    try:
        get_task(task_to_delete['task_id'])
        print("✗ Task should have been deleted but still exists")
        return False
    except TaskNotFoundError:
        print("✓ Confirmed task was deleted")
    
    # Test deleting non-existent task
    try:
        result = delete_task("nonexistent_id_12345")
        print(f"✓ Deletion of non-existent task returned: {result}")
        assert result == False
    except Exception as e:
        print(f"✗ Unexpected error deleting non-existent task: {e}")
        return False
    
    return True


def run_all_tests():
    """Run all task manager tests"""
    print("Starting Task Manager Tests")
    print("=" * 50)
    
    tests = [
        test_task_creation,
        test_task_listing,
        test_task_retrieval,
        test_task_update,
        test_tag_management,
        test_comment_management,
        test_task_deletion
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            print(f"✗ {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"Task Manager Tests Complete: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Task manager is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
