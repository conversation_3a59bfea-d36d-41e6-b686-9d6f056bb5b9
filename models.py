from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional, List, Literal
from datetime import datetime, date, time
from uuid import UUID, uuid4

class BaseConfig:
    model_config = ConfigDict(
        # Allow extra fields during updates
        extra='forbid',
        # Use enum values in JSON schema
        use_enum_values=True,
        # Validate assignment
        validate_assignment=True,
        # Convert strings to datetime automatically
        str_to_datetime=True
    )

# ============ NOTE MODELS ============
class NoteBase(BaseModel, BaseConfig):
    """Base model for notes with common fields"""
    id: UUID = Field(default_factory=uuid4, description = "Note ID")
    timestamp: str = Field(default_factory=datetime.now, description = "Timestamp of note creation")
    content: str = Field(...,min_lenght = 1, max_lenght = 10000, description = "Note content")
    title: str = Field(...,min_lenght = 1, max_lenght = 200, description = "Note title")

class NoteCreate(NoteBase):
    """Model for creating new notes"""
    pass

class NoteUpdate(BaseModel, BaseConfig):
    """Model for updating notes - all fields optional"""
    content: Optional[str] = Field(None, min_lenght = 1, max_lenght = 10000) #optional for update
    title: Optional[str] = Field(None, min_lenght = 1, max_lenght = 200)#optional for update

class NoteResponse(NoteBase):
    """Model for note responses from database"""
    id: UUID
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# ============ TASK MODELS ============
class TaskBase(BaseModel, BaseConfig):
    """Base model for tasks with common fields"""
    task_id: str = Field(default_factory=lambda: str(uuid4()), description="Task ID")
    task_name: str = Field(..., min_length=1, max_length=100, description="Task name")
    task_description: str = Field(..., min_length=1, max_length=2000, description="Task description")
    task_status: Literal["todo", "in_progress", "completed", "cancelled"] = Field(default="todo")
    task_priority: Literal["low", "medium", "high", "urgent"] = Field(default="medium")
    task_due_date: date = Field(..., description="Task due date")
    task_created_date: datetime = Field(default_factory=lambda: datetime.now(), description="Task creation date")
    task_modified_date: datetime = Field(default_factory=lambda: datetime.now(), description="Task modification date")
    task_completed_date: Optional[datetime] = Field(default=None, description="Task completion date")
    task_tags: List[str] = Field(default_factory=list, max_length=20, description="Task tags")
    task_comments: List[str] = Field(default_factory=list, max_length=50, description="Task comments")

    @field_validator('task_tags')
    @classmethod    
    def validate_tags(cls, v):
        """Validate task tags"""
        for tag in v:
            if len(tag.strip()) == 0:
                raise ValueError('Tags cannot be empty')
            if len(tag) > 50:
                raise ValueError('Tag length cannot exceed 50 characters')
        return [tag.strip().lower() for tag in v]

    
    @field_validator('task_comments')
    @classmethod
    def validate_comments(cls, v):
        """Validate task comments"""
        for comment in v:
            if len(comment.strip()) == 0:
                raise ValueError('Comments cannot be empty')
            if len(comment) > 200:
                raise ValueError('Comment length cannot exceed 200 characters')
        return [comment.strip() for comment in v]

    @field_validator('task_created_date', 'task_modified_date', 'task_completed_date', 'task_due_date')
    @classmethod
    def validate_dates(cls, v):
        """Ensure dates are not in the future"""
        if v > datetime.now():
            raise ValueError('Date cannot be in the future')
        return v


class TaskCreate(TaskBase):
    """Model for creating new tasks"""
    pass

class TaskUpdate(BaseModel, BaseConfig):
    """Model for updating tasks - all fields optional"""
    task_name: Optional[str] = Field(None, min_length=1, max_length=100)
    task_description: Optional[str] = Field(None, min_length=1, max_length=2000)
    task_status: Optional[Literal["todo", "in_progress", "completed", "cancelled"]] = None
    task_priority: Optional[Literal["low", "medium", "high", "urgent"]] = None
    task_due_date: Optional[date] = None
    task_tags: Optional[List[str]] = None
    task_comments: Optional[List[str]] = None

class TaskResponse(TaskBase):
    """Model for task responses from database"""
    task_id: UUID
    task_created_date: datetime
    task_modified_date: datetime
    task_completed_date: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)

# ============ REMINDER MODELS ============
class ReminderBase(BaseModel, BaseConfig):
    """Base model for reminders with common fields"""
    reminder_id: str = Field(default_factory=lambda: str(uuid4()))
    reminder_name: str = Field(..., min_length=1, max_length=100, description="Reminder name")
    reminder_description: str = Field(..., min_length=1, max_length=1000, description="Reminder description")
    reminder_date: date = Field(..., description="Reminder date")
    reminder_time: time = Field(..., description="Reminder time")
    reminder_type: Literal["once", "daily", "weekly", "monthly"] = Field(default="once")

    @field_validator('reminder_date')
    @classmethod
    def validate_reminder_date(cls, v):
        """Ensure reminder date is not in the past"""
        if v < date.today():
            raise ValueError('Reminder date cannot be in the past')
        return v    

class ReminderCreate(ReminderBase):
    """Model for creating new reminders"""
    pass

class ReminderUpdate(BaseModel, BaseConfig):
    """Model for updating reminders - all fields optional"""
    reminder_name: Optional[str] = Field(None, min_length=1, max_length=100)
    reminder_description: Optional[str] = Field(None, min_length=1, max_length=1000)
    reminder_date: Optional[date] = None
    reminder_time: Optional[time] = None
    reminder_type: Optional[Literal["once", "daily", "weekly", "monthly"]] = None

class ReminderResponse(ReminderBase):
    """Model for reminder responses from database"""
    reminder_id: UUID
    reminder_created_date: datetime
    
    model_config = ConfigDict(from_attributes=True)

# ============ COMMON RESPONSE MODELS ============
class ErrorResponse(BaseModel):
    """Standard error response model"""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Error details")
    error_code: Optional[str] = Field(None, description="Error code")

class SuccessResponse(BaseModel):
    """Standard success response model"""
    message: str = Field(..., description="Success message")
    data: Optional[dict] = Field(None, description="Response data")

class PaginatedResponse(BaseModel):
    """Paginated response model"""
    items: List[dict] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    size: int = Field(..., description="Page size")
    pages: int = Field(..., description="Total number of pages")