"""
Centralized Data Models Module

This module provides a consistent data architecture across all tool managers using Pydantic models.
It ensures data validation, type safety, and consistent error handling throughout the application.

Architecture Pattern:
- BaseConfig: Shared configuration for all models
- Base Models: Define common fields and validation rules
- Create Models: Used for creating new entities (inherit from Base)
- Update Models: Used for updating entities (all fields optional)
- Response Models: Used for API responses and data retrieval

Data Flow:
1. Input data is validated using Create/Update models
2. Data is stored using the validated model's dict representation
3. Retrieved data can be validated using Response models
4. All models share consistent field validation and error handling

Benefits:
- Centralized validation logic
- Type safety across all managers
- Consistent error messages
- Easy to maintain and extend
- Automatic JSON serialization/deserialization
"""

from pydantic import BaseModel, Field, ConfigDict, field_validator
from typing import Optional, List, Literal
from datetime import datetime, date
from uuid import uuid4


class BaseConfig:
    """
    Base configuration class for all Pydantic models.

    This configuration ensures consistent behavior across all models:
    - Forbids extra fields to prevent data corruption
    - Uses enum values in JSON schema for better API documentation
    - Validates assignments to catch errors early
    - Automatically converts strings to datetime objects when possible
    """
    model_config = ConfigDict(
        # Prevent extra fields to maintain data integrity
        extra='forbid',
        # Use enum values in JSON schema for better documentation
        use_enum_values=True,
        # Validate field assignments to catch errors early
        validate_assignment=True,
        # Convert strings to datetime automatically when possible
        str_to_datetime=True
    )

# ============ NOTE MODELS ============
"""
Note Models for the Notes Management System

These models handle all note-related data validation and serialization.
Notes are simple text-based entities with titles and content.

Model Hierarchy:
- NoteBase: Contains all common fields (id, timestamp, content, title)
- NoteCreate: Used when creating new notes (inherits all fields from NoteBase)
- NoteUpdate: Used when updating notes (all fields optional for partial updates)
- NoteResponse: Used for API responses (includes metadata like created_at, updated_at)

Data Flow:
1. User input → NoteCreate model → validation → storage
2. Storage → NoteResponse model → user output
3. Update input → NoteUpdate model → validation → merge with existing → storage

Field Constraints:
- ID: Auto-generated UUID hex string for uniqueness
- Timestamp: ISO format string for cross-platform compatibility
- Content: 1-10000 characters (required)
- Title: 1-200 characters (required, auto-generated from first line if not provided)
"""

class NoteBase(BaseModel, BaseConfig):
    """
    Base model for notes containing all common fields.

    This model defines the core structure of a note with validation rules.
    All other note models inherit from this to ensure consistency.

    Fields:
        id (str): Unique identifier generated as UUID hex string
        timestamp (str): ISO format creation timestamp
        content (str): Main note content (1-10000 chars)
        title (str): Note title (1-200 chars)
    """
    id: str = Field(default_factory=lambda: uuid4().hex, description="Unique note identifier")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Note creation timestamp")
    content: str = Field(..., min_length=1, max_length=10000, description="Note content")
    title: str = Field(..., min_length=1, max_length=200, description="Note title")


class NoteCreate(NoteBase):
    """
    Model for creating new notes.

    Inherits all fields from NoteBase with their validation rules.
    Used by the note manager when creating new notes from user input.

    Usage:
        note_data = NoteCreate(content="My note", title="Important")
        # ID and timestamp are auto-generated
    """
    pass


class NoteUpdate(BaseModel, BaseConfig):
    """
    Model for updating existing notes.

    All fields are optional to allow partial updates. Only provided fields
    will be updated in the existing note.

    Usage:
        update_data = NoteUpdate(title="New Title")  # Only update title
        update_data = NoteUpdate(content="New content", title="New title")  # Update both
    """
    content: Optional[str] = Field(None, min_length=1, max_length=10000, description="Updated note content")
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="Updated note title")


class NoteResponse(NoteBase):
    """
    Model for note responses and API output.

    Extends NoteBase with additional metadata fields for tracking
    creation and modification times. Used when returning note data
    to users or external systems.

    Additional Fields:
        created_at (str): When the note was first created
        updated_at (str): When the note was last modified
    """
    id: str
    created_at: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat(), description="Creation timestamp")
    updated_at: Optional[str] = Field(default_factory=lambda: datetime.now().isoformat(), description="Last update timestamp")

    model_config = ConfigDict(from_attributes=True)

# ============ TASK MODELS ============
"""
Task Models for the Task Management System

These models handle all task-related data validation and serialization.
Tasks are complex entities with status tracking, priority levels, due dates,
tags, and comments for comprehensive project management.

Model Hierarchy:
- TaskBase: Contains all common fields with validation and business logic
- TaskCreate: Used when creating new tasks (inherits all fields from TaskBase)
- TaskUpdate: Used when updating tasks (all fields optional for partial updates)
- TaskResponse: Used for API responses (includes metadata and formatting)

Data Flow:
1. User input → TaskCreate model → validation → storage
2. Storage → TaskResponse model → user output
3. Update input → TaskUpdate model → validation → merge with existing → storage

Status Workflow:
- todo → in_progress → completed
- Any status can be changed to cancelled
- Completion date is auto-set when status becomes "completed"

Priority Levels:
- low: Non-urgent tasks
- medium: Standard priority (default)
- high: Important tasks requiring attention
- urgent: Critical tasks requiring immediate action

Field Constraints:
- ID: Auto-generated UUID string for uniqueness
- Name: 1-100 characters (required)
- Description: 1-2000 characters (required)
- Due date: Must be a valid date
- Tags: Max 20 tags, each max 50 characters, auto-normalized to lowercase
- Comments: Max 50 comments, each max 200 characters
"""

class TaskBase(BaseModel, BaseConfig):
    """
    Base model for tasks containing all common fields and validation logic.

    This model defines the complete structure of a task with comprehensive
    validation rules and business logic for task management.

    Fields:
        task_id (str): Unique identifier generated as UUID string
        task_name (str): Task name/title (1-100 chars)
        task_description (str): Detailed task description (1-2000 chars)
        task_status (Literal): Current status (todo, in_progress, completed, cancelled)
        task_priority (Literal): Priority level (low, medium, high, urgent)
        task_due_date (date): When the task is due
        task_created_date (datetime): When the task was created
        task_modified_date (datetime): When the task was last modified
        task_completed_date (Optional[datetime]): When the task was completed
        task_tags (List[str]): List of tags for categorization
        task_comments (List[str]): List of comments for collaboration
    """
    task_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique task identifier")
    task_name: str = Field(..., min_length=1, max_length=100, description="Task name")
    task_description: str = Field(..., min_length=1, max_length=2000, description="Task description")
    task_status: Literal["todo", "in_progress", "completed", "cancelled"] = Field(default="todo", description="Task status")
    task_priority: Literal["low", "medium", "high", "urgent"] = Field(default="medium", description="Task priority")
    task_due_date: str = Field(..., description="Task due date")
    task_created_date: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Task creation date")
    task_modified_date: str = Field(default_factory=lambda: datetime.now().isoformat(), description="Task modification date")
    task_completed_date: Optional[str] = Field(default=None, description="Task completion date")
    task_tags: List[str] = Field(default_factory=list, max_length=20, description="Task tags")
    task_comments: List[str] = Field(default_factory=list, max_length=50, description="Task comments")

    @field_validator('task_tags')
    @classmethod
    def validate_tags(cls, v):
        """
        Validate and normalize task tags.

        Rules:
        - Tags cannot be empty after stripping whitespace
        - Each tag must be 50 characters or less
        - Tags are automatically normalized to lowercase
        - Whitespace is stripped from both ends

        Args:
            v (List[str]): List of tag strings to validate

        Returns:
            List[str]: Normalized list of tags (lowercase, stripped)

        Raises:
            ValueError: If any tag is empty or too long
        """
        for tag in v:
            if len(tag.strip()) == 0:
                raise ValueError('Tags cannot be empty')
            if len(tag) > 50:
                raise ValueError('Tag length cannot exceed 50 characters')
        return [tag.strip().lower() for tag in v]


    @field_validator('task_comments')
    @classmethod
    def validate_comments(cls, v):
        """
        Validate and normalize task comments.

        Rules:
        - Comments cannot be empty after stripping whitespace
        - Each comment must be 200 characters or less
        - Whitespace is stripped from both ends

        Args:
            v (List[str]): List of comment strings to validate

        Returns:
            List[str]: Normalized list of comments (stripped)

        Raises:
            ValueError: If any comment is empty or too long
        """
        for comment in v:
            if len(comment.strip()) == 0:
                raise ValueError('Comments cannot be empty')
            if len(comment) > 200:
                raise ValueError('Comment length cannot exceed 200 characters')
        return [comment.strip() for comment in v]

    @field_validator('task_created_date', 'task_modified_date', 'task_completed_date')
    @classmethod
    def validate_dates(cls, v):
        """
        Validate datetime fields to ensure they are reasonable.

        Rules:
        - Dates cannot be in the future (prevents data entry errors)
        - None values are allowed for optional fields

        Args:
            v (datetime or None): Datetime value to validate

        Returns:
            datetime or None: The validated datetime value

        Raises:
            ValueError: If date is in the future
        """
        if v and isinstance(v, datetime) and v > datetime.now():
            raise ValueError('Date cannot be in the future')
        return v


class TaskCreate(TaskBase):
    """
    Model for creating new tasks.

    Inherits all fields from TaskBase with their validation rules.
    Used by the task manager when creating new tasks from user input.

    Usage:
        task_data = TaskCreate(
            task_name="Complete project",
            task_description="Finish the final implementation",
            task_due_date=date(2024, 12, 31),
            task_priority="high"
        )
        # ID and timestamps are auto-generated
    """
    pass


class TaskUpdate(BaseModel, BaseConfig):
    """
    Model for updating existing tasks.

    All fields are optional to allow partial updates. Only provided fields
    will be updated in the existing task. Inherits validation from TaskBase
    for any provided fields.

    Usage:
        update_data = TaskUpdate(task_status="completed")  # Only update status
        update_data = TaskUpdate(task_name="New name", task_priority="urgent")  # Update multiple fields
    """
    task_name: Optional[str] = Field(None, min_length=1, max_length=100, description="Updated task name")
    task_description: Optional[str] = Field(None, min_length=1, max_length=2000, description="Updated task description")
    task_status: Optional[Literal["todo", "in_progress", "completed", "cancelled"]] = Field(None, description="Updated task status")
    task_priority: Optional[Literal["low", "medium", "high", "urgent"]] = Field(None, description="Updated task priority")
    task_due_date: Optional[str] = Field(None, description="Updated due date")
    task_tags: Optional[List[str]] = Field(None, description="Updated task tags")
    task_comments: Optional[List[str]] = Field(None, description="Updated task comments")


class TaskResponse(TaskBase):
    """
    Model for task responses and API output.

    Extends TaskBase with string representations of datetime fields
    for consistent JSON serialization. Used when returning task data
    to users or external systems.

    Note: Datetime fields are converted to strings for JSON compatibility
    while maintaining the same validation rules from TaskBase.
    """
    task_id: str
    task_created_date: str
    task_modified_date: str
    task_completed_date: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

# ============ REMINDER MODELS ============
"""
Reminder Models for the Reminder Management System

These models handle all reminder-related data validation and serialization.
Reminders are time-based notifications with support for recurring patterns.

Model Hierarchy:
- ReminderBase: Contains all common fields with validation and business logic
- ReminderCreate: Used when creating new reminders (inherits all fields from ReminderBase)
- ReminderUpdate: Used when updating reminders (all fields optional for partial updates)
- ReminderResponse: Used for API responses (includes metadata and formatting)

Data Flow:
1. User input → ReminderCreate model → validation → storage
2. Storage → ReminderResponse model → user output
3. Update input → ReminderUpdate model → validation → merge with existing → storage

Reminder Types:
- once: Single occurrence reminder (default)
- daily: Repeats every day at the specified time
- weekly: Repeats every week on the same day and time
- monthly: Repeats every month on the same date and time

Field Constraints:
- ID: Auto-generated UUID string for uniqueness
- Name: 1-100 characters (required)
- Description: 1-1000 characters (required)
- Date: Must be today or in the future (prevents past reminders)
- Time: Valid time format for scheduling
- Type: Must be one of the supported recurrence patterns
"""

class ReminderBase(BaseModel, BaseConfig):
    """
    Base model for reminders containing all common fields and validation logic.

    This model defines the complete structure of a reminder with comprehensive
    validation rules and business logic for reminder scheduling.

    Fields:
        reminder_id (str): Unique identifier generated as UUID string
        reminder_name (str): Reminder name/title (1-100 chars)
        reminder_description (str): Detailed reminder description (1-1000 chars)
        reminder_date (date): When the reminder should trigger
        reminder_time (time): What time the reminder should trigger
        reminder_type (Literal): Recurrence pattern (once, daily, weekly, monthly)
    """
    reminder_id: str = Field(default_factory=lambda: str(uuid4()), description="Unique reminder identifier")
    reminder_name: str = Field(..., min_length=1, max_length=100, description="Reminder name")
    reminder_description: str = Field(..., min_length=1, max_length=1000, description="Reminder description")
    reminder_date: str = Field(..., description="Reminder date")
    reminder_time: str = Field(..., description="Reminder time")
    reminder_type: Literal["once", "daily", "weekly", "monthly"] = Field(default="once", description="Reminder recurrence type")

    @field_validator('reminder_date')
    @classmethod
    def validate_reminder_date(cls, v):
        """
        Validate reminder date to ensure it's not in the past.

        Rules:
        - Reminder date must be today or in the future
        - Prevents creation of reminders that have already passed
        - Handles both date objects and string representations

        Args:
            v (date or str): Date value to validate

        Returns:
            date or str: The validated date value

        Raises:
            ValueError: If date is in the past
        """
        # Handle string dates (convert for comparison but return original)
        if isinstance(v, str):
            try:
                from datetime import datetime
                parsed_date = datetime.strptime(v, '%Y-%m-%d').date()
                if parsed_date < date.today():
                    raise ValueError('Reminder date cannot be in the past')
            except ValueError as e:
                if 'cannot be in the past' in str(e):
                    raise e
                # If it's a parsing error, let it pass through for now
                pass
            return v
        elif isinstance(v, date) and v < date.today():
            raise ValueError('Reminder date cannot be in the past')
        return v


class ReminderCreate(ReminderBase):
    """
    Model for creating new reminders.

    Inherits all fields from ReminderBase with their validation rules.
    Used by the reminder manager when creating new reminders from user input.

    Usage:
        reminder_data = ReminderCreate(
            reminder_name="Doctor appointment",
            reminder_description="Annual checkup with Dr. Smith",
            reminder_date=date(2024, 12, 15),
            reminder_time=time(14, 30),
            reminder_type="once"
        )
        # ID is auto-generated
    """
    pass


class ReminderUpdate(BaseModel, BaseConfig):
    """
    Model for updating existing reminders.

    All fields are optional to allow partial updates. Only provided fields
    will be updated in the existing reminder. Inherits validation from ReminderBase
    for any provided fields.

    Usage:
        update_data = ReminderUpdate(reminder_date=date(2024, 12, 20))  # Only update date
        update_data = ReminderUpdate(reminder_name="New name", reminder_type="daily")  # Update multiple fields
    """
    reminder_name: Optional[str] = Field(None, min_length=1, max_length=100, description="Updated reminder name")
    reminder_description: Optional[str] = Field(None, min_length=1, max_length=1000, description="Updated reminder description")
    reminder_date: Optional[str] = Field(None, description="Updated reminder date")
    reminder_time: Optional[str] = Field(None, description="Updated reminder time")
    reminder_type: Optional[Literal["once", "daily", "weekly", "monthly"]] = Field(None, description="Updated reminder type")


class ReminderResponse(ReminderBase):
    """
    Model for reminder responses and API output.

    Extends ReminderBase with additional metadata fields for tracking
    creation times. Used when returning reminder data to users or external systems.

    Additional Fields:
        reminder_created_date (str): When the reminder was first created
    """
    reminder_id: str
    reminder_created_date: str

    model_config = ConfigDict(from_attributes=True)

# ============ COMMON RESPONSE MODELS ============
"""
Common Response Models for Consistent API Communication

These models provide standardized response formats across all tool managers.
They ensure consistent error handling, success responses, and pagination
throughout the application.

Model Types:
- ErrorResponse: Standardized error information with details and codes
- SuccessResponse: Standardized success information with optional data
- PaginatedResponse: Standardized pagination for large data sets

Benefits:
- Consistent API responses across all managers
- Standardized error handling and reporting
- Easy integration with frontend applications
- Clear separation of concerns between data and metadata
"""

class ErrorResponse(BaseModel):
    """
    Standard error response model for consistent error handling.

    This model provides a unified structure for all error responses
    across the application, making it easier for clients to handle
    errors consistently.

    Fields:
        error (str): Main error message (required)
        detail (str, optional): Additional error details or context
        error_code (str, optional): Machine-readable error code for programmatic handling

    Usage:
        error_response = ErrorResponse(
            error="Validation failed",
            detail="The 'name' field cannot be empty",
            error_code="VALIDATION_ERROR"
        )
    """
    error: str = Field(..., description="Main error message")
    detail: Optional[str] = Field(None, description="Additional error details or context")
    error_code: Optional[str] = Field(None, description="Machine-readable error code")


class SuccessResponse(BaseModel):
    """
    Standard success response model for consistent success handling.

    This model provides a unified structure for all success responses
    across the application, making it easier for clients to handle
    successful operations consistently.

    Fields:
        message (str): Success message describing what happened
        data (dict, optional): Additional response data or results

    Usage:
        success_response = SuccessResponse(
            message="Task created successfully",
            data={"task_id": "123", "status": "todo"}
        )
    """
    message: str = Field(..., description="Success message describing the operation")
    data: Optional[dict] = Field(None, description="Additional response data or results")


class PaginatedResponse(BaseModel):
    """
    Paginated response model for handling large data sets efficiently.

    This model provides a standardized way to return paginated results
    with metadata about the pagination state, making it easier for
    clients to implement pagination controls.

    Fields:
        items (List[dict]): The actual data items for the current page
        total (int): Total number of items across all pages
        page (int): Current page number (1-based)
        size (int): Number of items per page
        pages (int): Total number of pages available

    Usage:
        paginated_response = PaginatedResponse(
            items=[{"id": "1", "name": "Task 1"}, {"id": "2", "name": "Task 2"}],
            total=100,
            page=1,
            size=10,
            pages=10
        )
    """
    items: List[dict] = Field(..., description="List of items for the current page")
    total: int = Field(..., description="Total number of items across all pages")
    page: int = Field(..., description="Current page number (1-based)")
    size: int = Field(..., description="Number of items per page")
    pages: int = Field(..., description="Total number of pages available")


# ============ ARCHITECTURE SUMMARY ============
"""
Models.py Architecture Summary

This module implements a consistent data architecture pattern across all tool managers:

1. CENTRALIZED VALIDATION
   - All data validation rules are defined in one place
   - Consistent field constraints and error messages
   - Reusable validation logic across managers

2. INHERITANCE HIERARCHY
   - Base models define core fields and validation
   - Create models inherit everything for new entity creation
   - Update models make all fields optional for partial updates
   - Response models add metadata for API responses

3. CONSISTENT PATTERNS
   - All entities follow the same Base → Create → Update → Response pattern
   - Standardized field naming conventions (entity_field_name)
   - Consistent error handling and validation messages

4. TYPE SAFETY
   - Full type hints for better IDE support and error catching
   - Pydantic validation ensures runtime type safety
   - Literal types for controlled vocabularies (status, priority, etc.)

5. JSON COMPATIBILITY
   - All models serialize/deserialize to/from JSON cleanly
   - String representations of complex types (UUID, datetime) for storage
   - Consistent field naming for API compatibility

6. EXTENSIBILITY
   - Easy to add new entity types following the same pattern
   - Validation rules can be extended without breaking existing code
   - New fields can be added with proper defaults and validation

This architecture ensures that all tool managers (notes, tasks, reminders)
behave consistently and can be easily maintained and extended.
"""